# PowerShell script to run the OTP delivery system
Write-Host "🚀 Starting OTP Delivery System" -ForegroundColor Green

# Step 1: Build the auth service
Write-Host "`n🔨 Building auth service..." -ForegroundColor Yellow
docker-compose -f docker-compose.hybrid.yml build auth-service

# Step 2: Start all services
Write-Host "`n🚀 Starting all services..." -ForegroundColor Yellow
docker-compose -f docker-compose.hybrid.yml up -d

# Step 3: Wait for services to start
Write-Host "`n⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Step 4: Check service status
Write-Host "`n📊 Checking service status..." -ForegroundColor Yellow
docker-compose -f docker-compose.hybrid.yml ps

# Step 5: Run database migration
Write-Host "`n🗄️ Running database migration..." -ForegroundColor Yellow
docker exec zionix-be-v1-auth-service-1 alembic upgrade head

# Step 6: Test the system
Write-Host "`n🧪 Testing the system..." -ForegroundColor Yellow
python test_otp_simple.py

Write-Host "`n✅ System startup complete!" -ForegroundColor Green
Write-Host "`n📊 Access Points:" -ForegroundColor Cyan
Write-Host "- Auth Service: http://localhost:8002" -ForegroundColor White
Write-Host "- Admin Service: http://localhost:8001" -ForegroundColor White  
Write-Host "- Flower (Celery): http://localhost:5555" -ForegroundColor White
Write-Host "- PgAdmin: http://localhost:5050" -ForegroundColor White
