"""
Enterprise OTP Delivery Service
Multi-channel OTP delivery with Redis queue, retry logic, and status tracking
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import json

import redis
from celery import Celery
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.core.config import settings
from app.db.session import get_db
from app.models.otp import OTP
from app.core.email import send_otp_email

logger = logging.getLogger(__name__)

# Redis connection for message queue
redis_client = redis.Redis.from_url(settings.REDIS_URL)

# Import Celery app from centralized configuration
from app.core.celery_app import celery_app

# Celery configuration is now handled in celery_app.py

class DeliveryStatus(str, Enum):
    PENDING = "pending"
    QUEUED = "queued"
    SENDING = "sending"
    DELIVERED = "delivered"
    FAILED = "failed"
    RETRY = "retry"

class DeliveryChannel(str, Enum):
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"

@dataclass
class DeliveryResult:
    success: bool
    channel: DeliveryChannel
    message: str
    delivery_id: Optional[str] = None
    error_code: Optional[str] = None
    retry_after: Optional[int] = None

class DeliveryRequest(BaseModel):
    otp_id: int
    user_id: int
    recipient_email: Optional[str] = None
    recipient_phone: Optional[str] = None
    channels: List[DeliveryChannel]
    priority: int = 1  # 1=high, 2=normal, 3=low
    max_retries: int = 3
    retry_delay: int = 60  # seconds

class BaseDeliveryChannel(ABC):
    """Abstract base class for delivery channels"""
    
    @abstractmethod
    async def send(self, request: DeliveryRequest, otp_code: str) -> DeliveryResult:
        """Send OTP through this channel"""
        pass
    
    @abstractmethod
    async def check_status(self, delivery_id: str) -> DeliveryStatus:
        """Check delivery status"""
        pass

class EmailDeliveryChannel(BaseDeliveryChannel):
    """Email delivery channel with SMTP and Gmail API fallback"""
    
    async def send(self, request: DeliveryRequest, otp_code: str) -> DeliveryResult:
        try:
            if not request.recipient_email:
                return DeliveryResult(
                    success=False,
                    channel=DeliveryChannel.EMAIL,
                    message="No email address provided",
                    error_code="NO_EMAIL"
                )
            
            # Try sending via configured email service
            success = await send_otp_email(
                email=request.recipient_email,
                otp_code=otp_code,
                user_name="User"
            )
            
            if success:
                return DeliveryResult(
                    success=True,
                    channel=DeliveryChannel.EMAIL,
                    message="Email sent successfully",
                    delivery_id=f"email_{request.otp_id}_{datetime.now().timestamp()}"
                )
            else:
                return DeliveryResult(
                    success=False,
                    channel=DeliveryChannel.EMAIL,
                    message="Failed to send email",
                    error_code="SMTP_ERROR",
                    retry_after=300  # Retry after 5 minutes
                )
                
        except Exception as e:
            logger.error(f"Email delivery error: {str(e)}")
            return DeliveryResult(
                success=False,
                channel=DeliveryChannel.EMAIL,
                message=f"Email delivery exception: {str(e)}",
                error_code="EXCEPTION",
                retry_after=600  # Retry after 10 minutes
            )
    
    async def check_status(self, delivery_id: str) -> DeliveryStatus:
        # For email, we assume delivered if sent successfully
        # In production, you might integrate with email service webhooks
        return DeliveryStatus.DELIVERED

class SMSDeliveryChannel(BaseDeliveryChannel):
    """SMS delivery channel using Gammu SMS gateway"""
    
    def __init__(self):
        # Initialize Gammu SMS gateway connection
        # This would be configured based on your SMS gateway setup
        self.gateway_url = settings.SMS_GATEWAY_URL if hasattr(settings, 'SMS_GATEWAY_URL') else None
        self.enabled = bool(self.gateway_url)
    
    async def send(self, request: DeliveryRequest, otp_code: str) -> DeliveryResult:
        if not self.enabled:
            return DeliveryResult(
                success=False,
                channel=DeliveryChannel.SMS,
                message="SMS gateway not configured",
                error_code="SMS_DISABLED"
            )
        
        try:
            if not request.recipient_phone:
                return DeliveryResult(
                    success=False,
                    channel=DeliveryChannel.SMS,
                    message="No phone number provided",
                    error_code="NO_PHONE"
                )
            
            # SMS sending logic would go here
            # For now, we'll simulate SMS sending
            message = f"Your Zionix verification code is: {otp_code}. Valid for 5 minutes."
            
            # Simulate SMS gateway call
            # In production, integrate with Gammu, Kannel, or similar
            await asyncio.sleep(0.1)  # Simulate network delay
            
            return DeliveryResult(
                success=True,
                channel=DeliveryChannel.SMS,
                message="SMS sent successfully",
                delivery_id=f"sms_{request.otp_id}_{datetime.now().timestamp()}"
            )
            
        except Exception as e:
            logger.error(f"SMS delivery error: {str(e)}")
            return DeliveryResult(
                success=False,
                channel=DeliveryChannel.SMS,
                message=f"SMS delivery exception: {str(e)}",
                error_code="EXCEPTION",
                retry_after=300
            )
    
    async def check_status(self, delivery_id: str) -> DeliveryStatus:
        # Check SMS delivery status via gateway API
        # For now, assume delivered
        return DeliveryStatus.DELIVERED

class OTPDeliveryService:
    """Main OTP delivery service with multi-channel support"""
    
    def __init__(self):
        self.channels = {
            DeliveryChannel.EMAIL: EmailDeliveryChannel(),
            DeliveryChannel.SMS: SMSDeliveryChannel(),
        }
        self.redis = redis_client
    
    async def queue_delivery(self, request: DeliveryRequest) -> bool:
        """Queue OTP delivery for async processing"""
        try:
            # Store delivery request in Redis
            request_key = f"otp_delivery:{request.otp_id}"
            request_data = request.model_dump()
            
            self.redis.setex(
                request_key,
                timedelta(hours=1),  # Expire after 1 hour
                json.dumps(request_data)
            )
            
            # Queue Celery task
            celery_app.send_task(
                'app.tasks.otp_tasks.deliver_otp_task',
                args=[request.otp_id, request.model_dump()],
                priority=request.priority,
                countdown=0  # Send immediately
            )
            
            # Update OTP status
            await self._update_otp_status(request.otp_id, DeliveryStatus.QUEUED)
            
            logger.info(f"OTP delivery queued for OTP {request.otp_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to queue OTP delivery: {str(e)}")
            return False
    
    async def deliver_otp(self, otp_id: int) -> DeliveryResult:
        """Deliver OTP through configured channels with fallback"""
        try:
            # Get delivery request from Redis
            request_key = f"otp_delivery:{otp_id}"
            request_data = self.redis.get(request_key)
            
            if not request_data:
                return DeliveryResult(
                    success=False,
                    channel=DeliveryChannel.EMAIL,
                    message="Delivery request not found",
                    error_code="REQUEST_NOT_FOUND"
                )
            
            request = DeliveryRequest(**json.loads(request_data))
            
            # Get OTP from database
            db = next(get_db())
            otp = db.query(OTP).filter(OTP.id == otp_id).first()
            
            if not otp:
                return DeliveryResult(
                    success=False,
                    channel=DeliveryChannel.EMAIL,
                    message="OTP not found",
                    error_code="OTP_NOT_FOUND"
                )
            
            # Check if OTP is still valid
            if otp.expires_at < datetime.now(timezone.utc):
                return DeliveryResult(
                    success=False,
                    channel=DeliveryChannel.EMAIL,
                    message="OTP expired",
                    error_code="OTP_EXPIRED"
                )
            
            # Update status to sending
            await self._update_otp_status(otp_id, DeliveryStatus.SENDING)
            
            # Try each channel in order
            for channel in request.channels:
                if channel not in self.channels:
                    continue
                
                try:
                    result = await self.channels[channel].send(request, otp.otp_code)
                    
                    if result.success:
                        await self._update_otp_status(otp_id, DeliveryStatus.DELIVERED, channel.value)
                        await self._log_delivery_success(otp_id, channel, result)
                        return result
                    else:
                        await self._log_delivery_failure(otp_id, channel, result)
                        
                except Exception as e:
                    logger.error(f"Channel {channel} failed with exception: {str(e)}")
                    await self._log_delivery_failure(otp_id, channel, DeliveryResult(
                        success=False,
                        channel=channel,
                        message=str(e),
                        error_code="EXCEPTION"
                    ))
            
            # All channels failed
            await self._update_otp_status(otp_id, DeliveryStatus.FAILED)
            return DeliveryResult(
                success=False,
                channel=DeliveryChannel.EMAIL,
                message="All delivery channels failed",
                error_code="ALL_CHANNELS_FAILED"
            )
            
        except Exception as e:
            logger.error(f"OTP delivery error: {str(e)}")
            await self._update_otp_status(otp_id, DeliveryStatus.FAILED)
            return DeliveryResult(
                success=False,
                channel=DeliveryChannel.EMAIL,
                message=f"Delivery service error: {str(e)}",
                error_code="SERVICE_ERROR"
            )
    
    async def _update_otp_status(self, otp_id: int, status: DeliveryStatus, channel: Optional[str] = None):
        """Update OTP delivery status in database"""
        try:
            db = next(get_db())
            otp = db.query(OTP).filter(OTP.id == otp_id).first()
            if otp:
                # Add delivery tracking fields to OTP model if not exists
                if hasattr(otp, 'delivery_status'):
                    otp.delivery_status = status.value
                if hasattr(otp, 'delivery_channel') and channel:
                    otp.delivery_channel = channel
                if hasattr(otp, 'last_delivery_attempt'):
                    otp.last_delivery_attempt = datetime.now(timezone.utc)
                
                db.commit()
                
        except Exception as e:
            logger.error(f"Failed to update OTP status: {str(e)}")
    
    async def _log_delivery_success(self, otp_id: int, channel: DeliveryChannel, result: DeliveryResult):
        """Log successful delivery"""
        logger.info(f"OTP {otp_id} delivered successfully via {channel.value}: {result.message}")
    
    async def _log_delivery_failure(self, otp_id: int, channel: DeliveryChannel, result: DeliveryResult):
        """Log failed delivery"""
        logger.warning(f"OTP {otp_id} delivery failed via {channel.value}: {result.message}")

# Celery tasks are now defined in app.tasks.otp_tasks

# Service instance
otp_delivery_service = OTPDeliveryService()
