<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration with Email Verification - Zionix</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .step.active {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .step.completed {
            border-left-color: #6c757d;
            background: #e2e3e5;
        }
        input, button {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        .message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .progress {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }
        .progress-step {
            flex: 1;
            text-align: center;
            padding: 10px;
            background: #e9ecef;
            margin: 0 5px;
            border-radius: 5px;
            font-weight: bold;
        }
        .progress-step.active {
            background: #28a745;
            color: white;
        }
        .progress-step.completed {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔐 ZIONIX</div>
            <h1>Registration with Email Verification Test</h1>
            <p>Test the complete registration flow with Google Email OTP verification</p>
        </div>

        <div class="progress">
            <div class="progress-step active" id="step1-progress">1. Register</div>
            <div class="progress-step" id="step2-progress">2. Verify Email</div>
            <div class="progress-step" id="step3-progress">3. Login</div>
        </div>

        <!-- Step 1: Registration -->
        <div class="step active" id="step1">
            <h3>Step 1: Register New Account</h3>
            <p>Enter your details to create a new account. You'll receive an email verification code.</p>
            
            <input type="text" id="firstName" placeholder="First Name" required>
            <input type="text" id="lastName" placeholder="Last Name" required>
            <input type="email" id="email" placeholder="Your Gmail Address" required>
            <input type="password" id="password" placeholder="Password (min 8 chars)" required>
            <input type="password" id="confirmPassword" placeholder="Confirm Password" required>
            
            <button onclick="registerUser()">Register Account</button>
            
            <div class="info">
                <strong>📧 Email Requirements:</strong>
                <ul>
                    <li>Use a real Gmail address you can access</li>
                    <li>Check your inbox (and spam folder) for the verification code</li>
                    <li>The verification code expires in 5 minutes</li>
                </ul>
            </div>
        </div>

        <!-- Step 2: Email Verification -->
        <div class="step" id="step2" style="display: none;">
            <h3>Step 2: Verify Your Email</h3>
            <p>We've sent a 6-digit verification code to: <strong id="registeredEmail"></strong></p>
            
            <input type="text" id="verificationCode" placeholder="Enter 6-digit verification code" maxlength="6" required>
            
            <button onclick="verifyEmail()">Verify Email</button>
            <button onclick="resendVerification()" style="background: #6c757d;">Resend Code</button>
            
            <div class="info">
                <strong>🔍 Can't find the email?</strong>
                <ul>
                    <li>Check your spam/junk folder</li>
                    <li>Wait a few minutes for delivery</li>
                    <li>Click "Resend Code" if needed</li>
                </ul>
            </div>
        </div>

        <!-- Step 3: Login Test -->
        <div class="step" id="step3" style="display: none;">
            <h3>Step 3: Test Login</h3>
            <p>Your account is now verified! Test logging in with your credentials.</p>
            
            <input type="email" id="loginEmail" placeholder="Email" readonly>
            <input type="password" id="loginPassword" placeholder="Password">
            
            <button onclick="testLogin()">Login</button>
            
            <div class="info">
                <strong>✅ Account Verified Successfully!</strong>
                <p>Your account is now active and you can log in to access all features.</p>
            </div>
        </div>

        <!-- Messages -->
        <div id="messages"></div>

        <!-- API Information -->
        <div class="info">
            <h4>🔧 API Endpoints Being Tested:</h4>
            <ul>
                <li><strong>POST /api/v1/register</strong> - Create account and send verification email</li>
                <li><strong>POST /api/v1/verify-email</strong> - Verify email with OTP code</li>
                <li><strong>POST /api/v1/resend-verification</strong> - Resend verification email</li>
                <li><strong>POST /api/v1/login</strong> - Login with verified account</li>
            </ul>
            <p><strong>Auth Service:</strong> <span id="serviceUrl">http://localhost:8002</span></p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8002/api/v1';
        let currentEmail = '';

        function showMessage(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = message;
            messagesDiv.appendChild(messageDiv);
            
            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 10000);
        }

        function updateProgress(step) {
            // Reset all steps
            for (let i = 1; i <= 3; i++) {
                document.getElementById(`step${i}-progress`).className = 'progress-step';
                document.getElementById(`step${i}`).style.display = 'none';
                document.getElementById(`step${i}`).className = 'step';
            }
            
            // Update current step
            document.getElementById(`step${step}-progress`).className = 'progress-step active';
            document.getElementById(`step${step}`).style.display = 'block';
            document.getElementById(`step${step}`).className = 'step active';
            
            // Mark completed steps
            for (let i = 1; i < step; i++) {
                document.getElementById(`step${i}-progress`).className = 'progress-step completed';
                document.getElementById(`step${i}`).className = 'step completed';
            }
        }

        async function registerUser() {
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (!firstName || !lastName || !email || !password || !confirmPassword) {
                showMessage('❌ Please fill in all fields.', 'error');
                return;
            }

            if (password !== confirmPassword) {
                showMessage('❌ Passwords do not match.', 'error');
                return;
            }

            if (password.length < 8) {
                showMessage('❌ Password must be at least 8 characters long.', 'error');
                return;
            }

            try {
                showMessage('🔄 Creating your account...', 'loading');

                const response = await fetch(`${API_BASE}/register`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        email: email,
                        first_name: firstName,
                        last_name: lastName,
                        password: password,
                        confirm_password: confirmPassword
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    currentEmail = email;
                    document.getElementById('registeredEmail').textContent = email;
                    document.getElementById('loginEmail').value = email;
                    
                    showMessage('✅ Account created! Check your email for verification code.', 'success');
                    updateProgress(2);
                } else {
                    showMessage(`❌ Registration failed: ${data.detail || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function verifyEmail() {
            const verificationCode = document.getElementById('verificationCode').value;

            if (!verificationCode || verificationCode.length !== 6) {
                showMessage('❌ Please enter a valid 6-digit verification code.', 'error');
                return;
            }

            try {
                showMessage('🔄 Verifying your email...', 'loading');

                const response = await fetch(`${API_BASE}/verify-email`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: currentEmail,
                        otp_code: verificationCode
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage('🎉 Email verified successfully! Your account is now active.', 'success');
                    updateProgress(3);
                } else {
                    showMessage(`❌ Verification failed: ${data.detail || 'Invalid or expired code'}`, 'error');
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function resendVerification() {
            try {
                showMessage('🔄 Sending new verification code...', 'loading');

                const response = await fetch(`${API_BASE}/resend-verification`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: currentEmail
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage('📧 New verification code sent! Check your email.', 'success');
                } else if (response.status === 429) {
                    showMessage('⏰ Too many requests. Please wait before requesting another code.', 'error');
                } else {
                    showMessage(`❌ Failed to resend: ${data.detail || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            if (!password) {
                showMessage('❌ Please enter your password.', 'error');
                return;
            }

            try {
                showMessage('🔄 Logging in...', 'loading');

                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        username: email,
                        password: password
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage('🎉 Login successful! Registration and verification flow completed.', 'success');
                    showMessage(`🔑 Access Token: ${data.access_token.substring(0, 50)}...`, 'info');
                } else if (response.status === 403) {
                    showMessage('❌ Email not verified. Please complete email verification first.', 'error');
                } else {
                    showMessage(`❌ Login failed: ${data.detail || 'Invalid credentials'}`, 'error');
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, 'error');
            }
        }

        // Check service availability on page load
        window.onload = async function() {
            try {
                const response = await fetch(`${API_BASE.replace('/api/v1', '')}/health`);
                if (response.ok) {
                    showMessage('✅ Auth service is running and ready for testing!', 'success');
                } else {
                    showMessage('⚠️ Auth service may not be running. Please start it with: docker-compose -f docker-compose.hybrid.yml up -d', 'error');
                }
            } catch (error) {
                showMessage('❌ Cannot connect to auth service. Please ensure it is running on localhost:8002', 'error');
            }
        };
    </script>
</body>
</html>
