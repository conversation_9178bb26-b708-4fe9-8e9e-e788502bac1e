from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import asyncio
from app.api.routes import auth, users, encryption, oauth
try:
    from app.api.routes import otp_analytics
    OTP_ANALYTICS_AVAILABLE = True
except ImportError:
    OTP_ANALYTICS_AVAILABLE = False
from app.core.config import settings
from app.db.session import engine
from app.db.base import Base
from app.events.kafka_client import KAFKA_ENABLED
from app.core.cache import cache

# Create database tables
Base.metadata.create_all(bind=engine)

# Kafka and Redis startup and shutdown using lifespan
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("Starting auth service startup process...")

    # Initialize Redis cache
    try:
        redis_connected = await cache.connect()
        if redis_connected:
            print("Redis cache connected successfully")
        else:
            print("Redis cache connection failed or disabled")
    except Exception as e:
        print(f"Warning: Redis initialization issue: {str(e)}")

    if KAFKA_ENABLED:
        try:
            # Initialize Kafka producer (will be created on first use)
            print("Kafka is enabled for auth service")
        except Exception as e:
            print(f"Warning: Kafka initialization issue: {str(e)}")
    else:
        print("Kafka is disabled for auth service")

    print("Auth service startup complete")

    yield

    # Shutdown
    try:
        # Close Redis connection
        await cache.disconnect()
        print("Redis cache disconnected")
    except Exception as e:
        print(f"Warning: Error during Redis shutdown: {str(e)}")

    try:
        # Close Kafka producer
        from app.events.kafka_client import close_producer
        await close_producer()
        print("Kafka producer closed")
    except Exception as e:
        print(f"Warning: Error during Kafka shutdown: {str(e)}")

app = FastAPI(
    title=settings.PROJECT_NAME,
    description="Authentication Service API for ZCare Platform",
    version="0.1.0",
    openapi_url="/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Customize OpenAPI schema to show "email" instead of "username" in login form
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    from fastapi.openapi.utils import get_openapi
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

# Set CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS if settings.CORS_ORIGINS else ["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router, prefix=settings.API_V1_STR)
app.include_router(users.router, prefix=settings.API_V1_STR)
app.include_router(encryption.router, prefix=settings.API_V1_STR)
app.include_router(oauth.router, prefix=settings.API_V1_STR)

# Include OTP analytics router if available
if OTP_ANALYTICS_AVAILABLE:
    app.include_router(otp_analytics.router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    return {"message": "Welcome to ZCare Auth Service"}

@app.get("/health")
async def health_check():
    # Check Redis health
    redis_health = await cache.health_check()

    return {
        "status": "healthy",
        "redis": redis_health
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=8001, reload=True)