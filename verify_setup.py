#!/usr/bin/env python3
"""
Quick Setup Verification Script
Checks if all files and configurations are in place
"""

import os
import sys
from pathlib import Path

def check_file_exists(file_path: str, description: str) -> bool:
    """Check if a file exists"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - NOT FOUND")
        return False

def check_directory_exists(dir_path: str, description: str) -> bool:
    """Check if a directory exists"""
    if os.path.isdir(dir_path):
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} - NOT FOUND")
        return False

def check_file_content(file_path: str, search_text: str, description: str) -> bool:
    """Check if a file contains specific text"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if search_text in content:
                print(f"✅ {description}: Found in {file_path}")
                return True
            else:
                print(f"❌ {description}: Not found in {file_path}")
                return False
    except Exception as e:
        print(f"❌ {description}: Error reading {file_path} - {e}")
        return False

def main():
    print("🔍 OTP Delivery System Setup Verification")
    print("=" * 50)
    
    checks_passed = 0
    total_checks = 0
    
    # Check core files
    files_to_check = [
        ("auth-service/requirements.txt", "Requirements file"),
        ("auth-service/app/core/config.py", "Configuration file"),
        ("auth-service/app/core/otp_delivery.py", "OTP delivery service"),
        ("auth-service/app/core/sms_gateway.py", "SMS gateway service"),
        ("auth-service/app/core/celery_app.py", "Celery configuration"),
        ("auth-service/app/models/otp.py", "Enhanced OTP model"),
        ("docker-compose.hybrid.yml", "Docker Compose file"),
        (".env", "Environment file"),
    ]
    
    print("\n📁 Checking Core Files:")
    for file_path, description in files_to_check:
        if check_file_exists(file_path, description):
            checks_passed += 1
        total_checks += 1
    
    # Check directories
    directories_to_check = [
        ("auth-service/app/tasks", "Tasks directory"),
        ("auth-service/app/db/migrations/versions", "Migrations directory"),
        ("auth-service/app/api/routes", "API routes directory"),
    ]
    
    print("\n📂 Checking Directories:")
    for dir_path, description in directories_to_check:
        if check_directory_exists(dir_path, description):
            checks_passed += 1
        total_checks += 1
    
    # Check task files
    task_files = [
        ("auth-service/app/tasks/__init__.py", "Tasks init file"),
        ("auth-service/app/tasks/otp_tasks.py", "OTP tasks"),
        ("auth-service/app/tasks/cleanup_tasks.py", "Cleanup tasks"),
    ]
    
    print("\n📋 Checking Task Files:")
    for file_path, description in task_files:
        if check_file_exists(file_path, description):
            checks_passed += 1
        total_checks += 1
    
    # Check migration files
    migration_files = [
        ("auth-service/app/db/migrations/versions/008_enhance_otps_delivery_tracking.py", "OTP enhancement migration"),
    ]
    
    print("\n🗄️ Checking Migration Files:")
    for file_path, description in migration_files:
        if check_file_exists(file_path, description):
            checks_passed += 1
        total_checks += 1
    
    # Check configuration content
    config_checks = [
        ("auth-service/requirements.txt", "celery", "Celery dependency"),
        ("auth-service/requirements.txt", "python-gammu", "Gammu dependency"),
        ("auth-service/requirements.txt", "flower", "Flower dependency"),
        (".env", "OTP_DELIVERY_ENABLED", "OTP delivery configuration"),
        (".env", "SMS_GATEWAY_TYPE", "SMS gateway configuration"),
        (".env", "CELERY_BROKER_URL", "Celery configuration"),
        ("docker-compose.hybrid.yml", "celery-worker", "Celery worker service"),
        ("docker-compose.hybrid.yml", "flower", "Flower service"),
    ]
    
    print("\n⚙️ Checking Configuration Content:")
    for file_path, search_text, description in config_checks:
        if check_file_content(file_path, search_text, description):
            checks_passed += 1
        total_checks += 1
    
    # Check test files
    test_files = [
        ("test_otp_delivery_system.py", "Main test script"),
        ("gammu-config-template.txt", "Gammu configuration template"),
    ]
    
    print("\n🧪 Checking Test Files:")
    for file_path, description in test_files:
        if check_file_exists(file_path, description):
            checks_passed += 1
        total_checks += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📊 VERIFICATION SUMMARY")
    print(f"Checks passed: {checks_passed}/{total_checks}")
    
    success_rate = (checks_passed / total_checks) * 100
    
    if success_rate == 100:
        print("🎉 All checks passed! Setup is complete.")
        print("\n🚀 Next steps:")
        print("1. Start the system: docker-compose -f docker-compose.hybrid.yml up -d")
        print("2. Run tests: python test_otp_delivery_system.py")
        print("3. Monitor with Flower: http://localhost:5555")
    elif success_rate >= 80:
        print("⚠️ Most checks passed. Review failed items.")
        print("\n🔧 To fix issues:")
        print("1. Check file paths and permissions")
        print("2. Verify all code was saved correctly")
        print("3. Re-run this verification script")
    else:
        print("❌ Multiple checks failed. Setup incomplete.")
        print("\n🛠️ To complete setup:")
        print("1. Review the implementation steps")
        print("2. Ensure all files are created")
        print("3. Check for typos in file paths")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
