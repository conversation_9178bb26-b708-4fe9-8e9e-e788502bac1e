# 🔐 Gmail OAuth Password Reset Implementation Guide

## 📋 Overview

This guide covers the implementation of a FastAPI-based password reset flow where OTP emails are sent using the logged-in user's Gmail account via Google OAuth 2.0 and the Gmail API.

## 🏗️ Architecture

### Flow Overview
1. **User Authentication** - User logs into Zionix with their account
2. **Google OAuth Authorization** - User authorizes Zionix to access their Gmail
3. **OTP Generation & Sending** - System generates OTP and sends via user's Gmail
4. **Password Reset** - Target user receives OTP and resets their password

### Key Components
- **Google OAuth 2.0** - For Gmail authorization
- **Gmail API** - For sending emails programmatically
- **JWT Authentication** - For Zionix user sessions
- **OTP System** - Secure one-time password generation

## 🚀 Setup Instructions

### Step 1: Google Cloud Console Setup

1. **Create Google Cloud Project:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one

2. **Enable Gmail API:**
   - Navigate to "APIs & Services" > "Library"
   - Search for "Gmail API" and enable it

3. **Create OAuth 2.0 Credentials:**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Application type: "Web application"
   - Name: "Zionix Auth Service"
   - Authorized redirect URIs: `http://localhost:8002/api/v1/auth/google/callback`

4. **Configure OAuth Consent Screen:**
   - Go to "APIs & Services" > "OAuth consent screen"
   - Add scopes: `gmail.send`, `userinfo.email`, `userinfo.profile`
   - Add test users if in development mode

### Step 2: Environment Configuration

Add to your `.env` file:

```env
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8002/api/v1/auth/google/callback
```

### Step 3: Database Migration

Run the database migration to create the OAuth tokens table:

```bash
# Navigate to auth-service directory
cd auth-service

# Run migration
alembic upgrade head
```

### Step 4: Install Dependencies

The required packages are already added to `requirements.txt`:

```bash
# Install dependencies
pip install -r requirements.txt
```

### Step 5: Start Services

```bash
# Start all services
docker-compose -f docker-compose.hybrid.yml up -d --build

# Check logs
docker-compose -f docker-compose.hybrid.yml logs auth-service
```

## 🔧 API Endpoints

### OAuth Management

#### 1. Authorize Gmail Access
**POST** `/api/v1/auth/google/authorize`

**Headers:**
```
Authorization: Bearer <access_token>
```

**Request:**
```json
{
  "state": "optional_state_parameter"
}
```

**Response:**
```json
{
  "authorization_url": "https://accounts.google.com/o/oauth2/auth?...",
  "state": "user_123_optional_state_parameter"
}
```

#### 2. OAuth Callback (handled automatically)
**POST** `/api/v1/auth/google/callback`

#### 3. Check OAuth Status
**GET** `/api/v1/auth/google/status`

**Headers:**
```
Authorization: Bearer <access_token>
```

**Response:**
```json
{
  "authorized": true,
  "has_valid_token": true,
  "is_expired": false,
  "provider_email": "<EMAIL>",
  "provider_name": "John Doe",
  "expires_at": "2024-01-01T12:00:00Z",
  "scope": "https://www.googleapis.com/auth/gmail.send ...",
  "can_send_email": true
}
```

### Password Reset via Gmail

#### 4. Send OTP via Gmail API
**POST** `/api/v1/forgot-password-gmail`

**Headers:**
```
Authorization: Bearer <access_token>
```

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "message": "OTP sent successfully via Gmail <NAME_EMAIL>. The code expires in 5 minutes.",
  "email": "<EMAIL>"
}
```

## 🧪 Testing

### Method 1: Interactive Web Test

Open `test_gmail_oauth_password_reset.html` in your browser for a complete guided test.

### Method 2: Manual API Testing

1. **Login to get access token:**
```bash
curl -X POST "http://localhost:8002/api/v1/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=password123"
```

2. **Get OAuth authorization URL:**
```bash
curl -X POST "http://localhost:8002/api/v1/auth/google/authorize" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{"state": "test"}'
```

3. **Visit authorization URL and complete OAuth flow**

4. **Check OAuth status:**
```bash
curl -X GET "http://localhost:8002/api/v1/auth/google/status" \
  -H "Authorization: Bearer <access_token>"
```

5. **Send OTP via Gmail:**
```bash
curl -X POST "http://localhost:8002/api/v1/forgot-password-gmail" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

## 🛡️ Security Features

### OAuth Security
- **State parameter** prevents CSRF attacks
- **PKCE flow** for enhanced security
- **Scope limitation** to only required permissions
- **Token expiration** and refresh handling

### Email Security
- **Professional templates** reduce phishing risk
- **Sender verification** via OAuth
- **Rate limiting** prevents abuse
- **Audit logging** for all operations

### API Security
- **JWT authentication** required for all operations
- **User isolation** - can only use own OAuth tokens
- **Permission validation** before sending emails
- **Error handling** doesn't leak sensitive information

## 📧 Email Template

The Gmail API sends professional emails with:

### Features
- **Zionix branding** and professional styling
- **Clear OTP code** prominently displayed
- **Security warnings** about code sharing
- **Expiration information** (5 minutes)
- **Professional formatting** to prevent phishing

### Content Structure
```
Subject: 🔐 Password Reset OTP - Zionix

Hello [User Name],

We received a request to reset your password. To proceed with your password reset, please use the following One-Time Password (OTP):

═══════════════════════════════════
OTP CODE: 123456
═══════════════════════════════════

🛡️ SECURITY INFORMATION:
• This OTP expires in 5 minutes
• Never share this code with anyone, including Zionix support
• We will never ask for your OTP via phone or email
• If you didn't request this reset, please ignore this email

Sent via Gmail API from: <EMAIL>
```

## 🔍 Troubleshooting

### Common Issues

1. **"OAuth credentials not configured"**
   - Check `GOOGLE_CLIENT_ID` and `GOOGLE_CLIENT_SECRET` in environment
   - Verify credentials in Google Cloud Console

2. **"Gmail send permission not granted"**
   - Re-authorize with correct scopes
   - Check OAuth consent screen configuration

3. **"Access token expired"**
   - System automatically refreshes tokens
   - User may need to re-authorize if refresh token expired

4. **"Failed to send email via Gmail API"**
   - Check Gmail API is enabled in Google Cloud Console
   - Verify OAuth token has `gmail.send` scope
   - Check API quotas and limits

### Debug Commands

```bash
# Check OAuth tokens in database
docker-compose -f docker-compose.hybrid.yml exec postgres-auth psql -U postgres -d auth_service -c "SELECT provider, provider_email, expires_at, is_active FROM oauth_tokens;"

# Check auth service logs
docker-compose -f docker-compose.hybrid.yml logs auth-service | grep -i oauth

# Test Gmail API connectivity
curl -X GET "http://localhost:8002/api/v1/auth/google/status" \
  -H "Authorization: Bearer <access_token>"
```

## 🎯 Benefits

### User Experience
- **Familiar OAuth flow** users trust
- **Professional emails** from known senders
- **No SMTP configuration** required
- **Real-time delivery** via Gmail

### Security
- **User-controlled sending** - emails come from authorized accounts
- **OAuth security model** with limited scopes
- **No stored email credentials** - tokens are refreshable
- **Audit trail** of all email operations

### Technical
- **Scalable architecture** using Google's infrastructure
- **High deliverability** via Gmail
- **Rate limiting** handled by Google
- **Enterprise-ready** with proper error handling

## 🚀 Production Deployment

### Environment Variables
```env
# Production OAuth settings
GOOGLE_CLIENT_ID=prod-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=prod-client-secret
GOOGLE_REDIRECT_URI=https://yourdomain.com/api/v1/auth/google/callback
```

### Security Considerations
- Use HTTPS for all OAuth redirects
- Implement proper CORS policies
- Monitor OAuth token usage
- Set up alerts for failed authentications
- Regular security audits of OAuth permissions

The Gmail OAuth password reset system is now **production-ready** with enterprise-grade security and user experience! 🎉
