@echo off
echo 🔐 Google Email OTP Status Check
echo ================================

echo.
echo 1. Checking if auth-service is running...
echo.

powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:8002/health' -Method GET; Write-Host '✅ Auth-service is running!' -ForegroundColor Green; Write-Host $response } catch { Write-Host '❌ Auth-service is not running. Please start it with: docker-compose -f docker-compose.hybrid.yml up -d' -ForegroundColor Red }"

echo.
echo 2. Checking OTP endpoints availability...
echo.

powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:8002/docs' -Method GET; Write-Host '✅ API documentation is accessible at http://localhost:8002/docs' -ForegroundColor Green } catch { Write-Host '❌ API docs not accessible' -ForegroundColor Red }"

echo.
echo 3. Testing forgot-password endpoint (without sending email)...
echo.

powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:8002/api/v1/forgot-password' -Method POST -ContentType 'application/json' -Body '{\"email\":\"<EMAIL>\"}'; Write-Host '✅ Forgot-password endpoint is working!' -ForegroundColor Green; Write-Host $response } catch { Write-Host '❌ Forgot-password endpoint error:' $_.Exception.Message -ForegroundColor Red }"

echo.
echo 4. Checking email configuration status...
echo.

powershell -Command "try { docker-compose -f docker-compose.hybrid.yml exec -T auth-service python -c 'from app.core.config import settings; print(f\"Mail server: {settings.MAIL_SERVER}\"); print(f\"Mail username: {settings.MAIL_USERNAME}\"); print(f\"OTP length: {settings.OTP_LENGTH}\"); print(f\"OTP expire minutes: {settings.OTP_EXPIRE_MINUTES}\")' } catch { Write-Host '❌ Could not check email configuration' -ForegroundColor Red }"

echo.
echo 5. Checking database connection...
echo.

powershell -Command "try { docker-compose -f docker-compose.hybrid.yml exec -T postgres-auth psql -U postgres -d auth_service -c 'SELECT COUNT(*) as otp_table_exists FROM information_schema.tables WHERE table_name = \"otps\";' } catch { Write-Host '❌ Database connection failed' -ForegroundColor Red }"

echo.
echo 📋 Summary:
echo =========
echo.
echo ✅ If all checks pass, your OTP system is ready!
echo 🔧 If any checks fail, follow the setup guide: GOOGLE_EMAIL_OTP_SETUP_GUIDE.md
echo 🧪 Test the OTP system: test_enhanced_otp_api.html
echo 📚 API Documentation: http://localhost:8002/docs
echo.

pause
