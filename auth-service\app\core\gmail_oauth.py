"""
Google OAuth 2.0 and Gmail API integration for sending OTP emails
using the logged-in user's Gmail account.
"""

import os
import json
import base64
import logging
from typing import Optional, Dict, Any
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

import httpx
from fastapi import HTT<PERSON>Exception, status
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from app.core.config import settings

logger = logging.getLogger(__name__)

# Google OAuth 2.0 configuration
SCOPES = [
    'https://www.googleapis.com/auth/gmail.send',
    'https://www.googleapis.com/auth/userinfo.email',
    'https://www.googleapis.com/auth/userinfo.profile'
]

class GoogleOAuthManager:
    """Manages Google OAuth 2.0 authentication and Gmail API operations"""
    
    def __init__(self):
        self.client_config = {
            "web": {
                "client_id": os.getenv("GOOGLE_CLIENT_ID"),
                "client_secret": os.getenv("GOOGLE_CLIENT_SECRET"),
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": [os.getenv("GOOGLE_REDIRECT_URI", "http://localhost:8002/api/v1/auth/google/callback")]
            }
        }
        
        if not self.client_config["web"]["client_id"] or not self.client_config["web"]["client_secret"]:
            logger.warning("Google OAuth credentials not configured. Gmail API features will be disabled.")
    
    def get_authorization_url(self, state: str = None) -> str:
        """Generate Google OAuth 2.0 authorization URL"""
        try:
            flow = Flow.from_client_config(
                self.client_config,
                scopes=SCOPES,
                state=state
            )
            flow.redirect_uri = self.client_config["web"]["redirect_uris"][0]
            
            authorization_url, _ = flow.authorization_url(
                access_type='offline',
                include_granted_scopes='true',
                prompt='consent'  # Force consent to get refresh token
            )
            
            return authorization_url
            
        except Exception as e:
            logger.error(f"Error generating authorization URL: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate authorization URL"
            )
    
    def exchange_code_for_tokens(self, authorization_code: str, state: str = None) -> Dict[str, Any]:
        """Exchange authorization code for access and refresh tokens"""
        try:
            flow = Flow.from_client_config(
                self.client_config,
                scopes=SCOPES,
                state=state
            )
            flow.redirect_uri = self.client_config["web"]["redirect_uris"][0]
            
            # Exchange code for tokens
            flow.fetch_token(code=authorization_code)
            
            credentials = flow.credentials
            
            # Get user info
            user_info = self._get_user_info(credentials)
            
            return {
                "access_token": credentials.token,
                "refresh_token": credentials.refresh_token,
                "expires_in": credentials.expiry.timestamp() if credentials.expiry else None,
                "scope": " ".join(credentials.scopes) if credentials.scopes else "",
                "user_info": user_info
            }
            
        except Exception as e:
            logger.error(f"Error exchanging code for tokens: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to exchange authorization code"
            )
    
    def _get_user_info(self, credentials: Credentials) -> Dict[str, Any]:
        """Get user information from Google API"""
        try:
            service = build('oauth2', 'v2', credentials=credentials)
            user_info = service.userinfo().get().execute()
            return user_info
        except Exception as e:
            logger.error(f"Error getting user info: {str(e)}")
            return {}
    
    def refresh_access_token(self, refresh_token: str) -> Dict[str, Any]:
        """Refresh access token using refresh token"""
        try:
            credentials = Credentials(
                token=None,
                refresh_token=refresh_token,
                token_uri=self.client_config["web"]["token_uri"],
                client_id=self.client_config["web"]["client_id"],
                client_secret=self.client_config["web"]["client_secret"]
            )
            
            # Refresh the token
            credentials.refresh(Request())
            
            return {
                "access_token": credentials.token,
                "expires_in": credentials.expiry.timestamp() if credentials.expiry else None
            }
            
        except Exception as e:
            logger.error(f"Error refreshing access token: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Failed to refresh access token"
            )
    
    def send_otp_via_gmail(
        self, 
        access_token: str, 
        recipient_email: str, 
        otp_code: str, 
        user_name: str = "User",
        sender_email: str = None
    ) -> bool:
        """Send OTP email using Gmail API with user's access token"""
        try:
            # Create credentials from access token
            credentials = Credentials(token=access_token)
            
            # Build Gmail service
            service = build('gmail', 'v1', credentials=credentials)
            
            # Get sender email if not provided
            if not sender_email:
                profile = service.users().getProfile(userId='me').execute()
                sender_email = profile.get('emailAddress')
            
            # Create email message
            message = self._create_otp_email_message(
                sender_email=sender_email,
                recipient_email=recipient_email,
                otp_code=otp_code,
                user_name=user_name
            )
            
            # Send email
            result = service.users().messages().send(
                userId='me',
                body={'raw': message}
            ).execute()
            
            logger.info(f"OTP email sent successfully via Gmail API to {recipient_email}. Message ID: {result.get('id')}")
            return True
            
        except HttpError as e:
            logger.error(f"Gmail API error sending OTP: {str(e)}")
            if e.resp.status == 401:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Gmail access token expired or invalid"
                )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send email via Gmail API"
            )
        except Exception as e:
            logger.error(f"Error sending OTP via Gmail API: {str(e)}")
            return False
    
    def _create_otp_email_message(
        self, 
        sender_email: str, 
        recipient_email: str, 
        otp_code: str, 
        user_name: str
    ) -> str:
        """Create RFC 2822 formatted email message for Gmail API"""
        
        # Create multipart message
        msg = MIMEMultipart('alternative')
        msg['From'] = sender_email
        msg['To'] = recipient_email
        msg['Subject'] = '🔐 Password Reset OTP - Zionix'
        
        # Create text and HTML versions
        text_content = f"""
🔐 ZIONIX - PASSWORD RESET REQUEST

Hello {user_name},

We received a request to reset your password. To proceed with your password reset, please use the following One-Time Password (OTP):

═══════════════════════════════════
OTP CODE: {otp_code}
═══════════════════════════════════

🛡️ SECURITY INFORMATION:
• This OTP expires in 5 minutes
• Never share this code with anyone, including Zionix support
• We will never ask for your OTP via phone or email
• If you didn't request this reset, please ignore this email

WHAT HAPPENS NEXT:
1. Enter this OTP on the password reset page
2. Create a new, strong password
3. You'll be able to log in with your new password

Best regards,
The Zionix Security Team

This is an automated security message sent via Gmail API.
© 2024 Zionix. All rights reserved.
        """
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Password Reset OTP - Zionix</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: #007bff; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 20px; background: #f8f9fa; }}
        .otp-box {{ background: #fff; border: 2px solid #007bff; padding: 20px; margin: 20px 0; text-align: center; }}
        .otp-code {{ font-size: 32px; font-weight: bold; color: #007bff; letter-spacing: 5px; }}
        .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 15px 0; }}
        .footer {{ text-align: center; color: #666; font-size: 12px; padding: 20px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 ZIONIX</h1>
            <h2>Password Reset Request</h2>
        </div>
        
        <div class="content">
            <p>Hello <strong>{user_name}</strong>,</p>
            
            <p>We received a request to reset your password. To proceed with your password reset, please use the following One-Time Password (OTP):</p>
            
            <div class="otp-box">
                <div>Your OTP Code:</div>
                <div class="otp-code">{otp_code}</div>
                <div>⏰ Expires in 5 minutes</div>
            </div>
            
            <div class="warning">
                <p><strong>🛡️ Security Information:</strong></p>
                <ul>
                    <li>This OTP expires in 5 minutes</li>
                    <li>Never share this code with anyone, including Zionix support</li>
                    <li>We will never ask for your OTP via phone or email</li>
                    <li>If you didn't request this reset, please ignore this email</li>
                </ul>
            </div>
            
            <p><strong>What happens next:</strong></p>
            <ol>
                <li>Enter this OTP on the password reset page</li>
                <li>Create a new, strong password</li>
                <li>You'll be able to log in with your new password</li>
            </ol>
            
            <p>Best regards,<br><strong>The Zionix Security Team</strong></p>
        </div>
        
        <div class="footer">
            <p>This is an automated security message sent via Gmail API.</p>
            <p>© 2024 Zionix. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
        """
        
        # Attach parts
        text_part = MIMEText(text_content, 'plain')
        html_part = MIMEText(html_content, 'html')
        
        msg.attach(text_part)
        msg.attach(html_part)
        
        # Encode message
        raw_message = base64.urlsafe_b64encode(msg.as_bytes()).decode('utf-8')
        return raw_message

# Global instance
gmail_oauth_manager = GoogleOAuthManager()
