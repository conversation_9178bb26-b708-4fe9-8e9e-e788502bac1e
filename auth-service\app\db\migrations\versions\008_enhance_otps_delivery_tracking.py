"""Enhance OTPs table with delivery tracking

Revision ID: 008_enhance_otps_delivery_tracking
Revises: 007_update_users_status_action
Create Date: 2024-01-15 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '008_enhance_otps_delivery_tracking'
down_revision = '007_update_users_status_action'
branch_labels = None
depends_on = None


def upgrade():
    # Add delivery tracking columns to otps table
    op.add_column('otps', sa.Column('delivery_status', sa.String(length=20), nullable=True, default='pending'))
    op.add_column('otps', sa.Column('delivery_channel', sa.String(length=10), nullable=True))
    op.add_column('otps', sa.Column('delivery_attempts', sa.Integer(), nullable=True, default=0))
    op.add_column('otps', sa.Column('last_delivery_attempt', sa.DateTime(timezone=True), nullable=True))
    op.add_column('otps', sa.Column('delivery_error', sa.Text(), nullable=True))
    op.add_column('otps', sa.Column('delivery_id', sa.String(length=100), nullable=True))
    op.add_column('otps', sa.Column('delivery_cost', sa.Float(), nullable=True, default=0.0))
    
    # Create indexes for better query performance
    op.create_index('idx_otps_delivery_status', 'otps', ['delivery_status'])
    op.create_index('idx_otps_delivery_channel', 'otps', ['delivery_channel'])
    op.create_index('idx_otps_user_purpose_status', 'otps', ['user_id', 'purpose', 'delivery_status'])
    op.create_index('idx_otps_last_delivery_attempt', 'otps', ['last_delivery_attempt'])
    
    # Update existing records to have default delivery status
    op.execute("UPDATE otps SET delivery_status = 'delivered', delivery_channel = 'email' WHERE delivery_status IS NULL")


def downgrade():
    # Drop indexes
    op.drop_index('idx_otps_last_delivery_attempt', table_name='otps')
    op.drop_index('idx_otps_user_purpose_status', table_name='otps')
    op.drop_index('idx_otps_delivery_channel', table_name='otps')
    op.drop_index('idx_otps_delivery_status', table_name='otps')
    
    # Drop columns
    op.drop_column('otps', 'delivery_cost')
    op.drop_column('otps', 'delivery_id')
    op.drop_column('otps', 'delivery_error')
    op.drop_column('otps', 'last_delivery_attempt')
    op.drop_column('otps', 'delivery_attempts')
    op.drop_column('otps', 'delivery_channel')
    op.drop_column('otps', 'delivery_status')
