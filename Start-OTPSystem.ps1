# PowerShell Script to Start OTP Delivery System
# Run this as Administrator

Write-Host "🚀 Starting OTP Delivery System" -ForegroundColor Green
Write-Host "=" * 50

# Function to run commands with error handling
function Invoke-SafeCommand {
    param(
        [string]$Command,
        [string]$Description,
        [int]$TimeoutSeconds = 60
    )
    
    Write-Host "`n🔧 $Description..." -ForegroundColor Yellow
    Write-Host "Command: $Command" -ForegroundColor Gray
    
    try {
        $job = Start-Job -ScriptBlock { 
            param($cmd)
            Invoke-Expression $cmd
        } -ArgumentList $Command
        
        if (Wait-Job $job -Timeout $TimeoutSeconds) {
            $result = Receive-Job $job
            Remove-Job $job
            
            if ($LASTEXITCODE -eq 0 -or $LASTEXITCODE -eq $null) {
                Write-Host "✅ $Description - SUCCESS" -ForegroundColor Green
                if ($result) {
                    Write-Host "Output: $($result | Out-String)" -ForegroundColor Gray
                }
                return $true
            } else {
                Write-Host "❌ $Description - FAILED (Exit Code: $LASTEXITCODE)" -ForegroundColor Red
                return $false
            }
        } else {
            Remove-Job $job -Force
            Write-Host "⏰ $Description - TIMEOUT" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ $Description - ERROR: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Step 1: Check Docker
Write-Host "`n📋 Checking Prerequisites..." -ForegroundColor Cyan

if (-not (Invoke-SafeCommand "docker --version" "Checking Docker" 10)) {
    Write-Host "❌ Docker is not available. Please:" -ForegroundColor Red
    Write-Host "1. Install Docker Desktop" -ForegroundColor Yellow
    Write-Host "2. Start Docker Desktop" -ForegroundColor Yellow
    Write-Host "3. Wait for Docker to fully start" -ForegroundColor Yellow
    exit 1
}

if (-not (Invoke-SafeCommand "docker-compose --version" "Checking Docker Compose" 10)) {
    Write-Host "❌ Docker Compose is not available." -ForegroundColor Red
    exit 1
}

# Step 2: Stop existing services
Write-Host "`n🛑 Stopping Existing Services..." -ForegroundColor Cyan
Invoke-SafeCommand "docker-compose -f docker-compose.hybrid.yml down" "Stopping existing services" 60

# Step 3: Build auth service
Write-Host "`n🔨 Building Services..." -ForegroundColor Cyan
if (-not (Invoke-SafeCommand "docker-compose -f docker-compose.hybrid.yml build auth-service" "Building auth service" 300)) {
    Write-Host "❌ Failed to build auth service. Check Docker and try again." -ForegroundColor Red
    exit 1
}

# Step 4: Start services
Write-Host "`n🚀 Starting Services..." -ForegroundColor Cyan
if (-not (Invoke-SafeCommand "docker-compose -f docker-compose.hybrid.yml up -d" "Starting all services" 120)) {
    Write-Host "❌ Failed to start services." -ForegroundColor Red
    exit 1
}

# Step 5: Wait for services
Write-Host "`n⏳ Waiting for services to initialize..." -ForegroundColor Cyan
Start-Sleep -Seconds 30

# Step 6: Check status
Write-Host "`n📊 Checking Service Status..." -ForegroundColor Cyan
Invoke-SafeCommand "docker-compose -f docker-compose.hybrid.yml ps" "Service status check" 30

# Step 7: Run migration
Write-Host "`n🗄️ Running Database Migration..." -ForegroundColor Cyan
Invoke-SafeCommand "docker exec zionix-be-v1-auth-service-1 alembic upgrade head" "Database migration" 60

# Step 8: Final status
Write-Host "`n🎉 OTP Delivery System Started!" -ForegroundColor Green
Write-Host "`n📊 Access Points:" -ForegroundColor Cyan
Write-Host "- Auth Service: http://localhost:8002" -ForegroundColor White
Write-Host "- Admin Service: http://localhost:8001" -ForegroundColor White
Write-Host "- Flower (Celery Monitor): http://localhost:5555" -ForegroundColor White
Write-Host "- PgAdmin: http://localhost:5050" -ForegroundColor White

Write-Host "`n🧪 To test the system:" -ForegroundColor Cyan
Write-Host "python test_otp_delivery_system.py" -ForegroundColor White

Write-Host "`n📝 To monitor logs:" -ForegroundColor Cyan
Write-Host "docker-compose -f docker-compose.hybrid.yml logs -f" -ForegroundColor White

Write-Host "`nPress any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
