# FastAPI and ASGI server
fastapi>=0.104.0,<0.105.0
uvicorn>=0.24.0,<0.25.0
pydantic>=2.0.0,<3.0.0
pydantic-settings>=2.0.0,<3.0.0
email-validator>=2.0.0,<3.0.0

# Database
sqlalchemy>=2.0.0,<3.0.0
psycopg2-binary>=2.9.6,<2.10.0
alembic>=1.11.0,<1.12.0

# Authentication
python-jose>=3.3.0,<3.4.0
passlib>=1.7.4,<1.8.0
python-multipart>=0.0.6,<0.1.0
bcrypt>=3.2.0,<3.3.0

# Cryptography for hybrid encryption
cryptography>=41.0.0,<42.0.0

# Kafka for events
aiokafka>=0.10.0,<0.13.0
kafka-python>=2.0.2,<3.0.0

# Redis for caching
redis>=4.5.5,<5.0.0

# Email
fastapi-mail>=1.4.1,<1.5.0

# Google OAuth and Gmail API
google-auth>=2.23.0,<3.0.0
google-auth-oauthlib>=1.1.0,<2.0.0
google-auth-httplib2>=0.1.1,<1.0.0
google-api-python-client>=2.100.0,<3.0.0
httpx>=0.25.0,<1.0.0

# OTP Delivery System
celery[redis]==5.3.4
python-gammu==3.2.4
flower==2.0.1
aiosmtplib==3.0.1
prometheus-client==0.19.0

# Utilities
python-dotenv>=0.21.0,<0.22.0
requests>=2.28.0,<2.29.0
tenacity>=8.2.2,<8.3.0