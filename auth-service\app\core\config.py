import os
from typing import List, Optional, Union
from pydantic import field_validator, computed_field
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    PROJECT_NAME: str = "ZCare Auth Service"
    API_V1_STR: str = "/api/v1"

    # CORS settings
    CORS_ORIGINS: List[str] = [
        "http://localhost:9080",  # APISIX Gateway
        "http://localhost:8002",  # Direct auth-service access
        "http://localhost:8080",  # Frontend server
        "*"                       # Allow all origins for development (including file://)
    ]

    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Database settings
    POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "localhost")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "Arunnathan")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "auth_service")

    @computed_field
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}/{self.POSTGRES_DB}"

    # JWT settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "supersecretkey")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "15"))  # 15 minutes
    REFRESH_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("REFRESH_TOKEN_EXPIRE_MINUTES", "10080"))  # 7 days

    # Email settings
    MAIL_USERNAME: str = os.getenv("MAIL_USERNAME", "")
    MAIL_PASSWORD: str = os.getenv("MAIL_PASSWORD", "")
    MAIL_FROM: str = os.getenv("MAIL_FROM", "<EMAIL>")
    MAIL_PORT: int = int(os.getenv("MAIL_PORT", "587"))
    MAIL_SERVER: str = os.getenv("MAIL_SERVER", "smtp.gmail.com")
    MAIL_FROM_NAME: str = os.getenv("MAIL_FROM_NAME", "Zionix Auth Service")

    # Redis Cache settings
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    REDIS_PASSWORD: Optional[str] = os.getenv("REDIS_PASSWORD", None)
    REDIS_URL: Optional[str] = os.getenv("REDIS_URL", None)

    # Cache configuration
    ENABLE_CACHE: bool = os.getenv("ENABLE_CACHE", "true").lower() == "true"
    CACHE_TTL: int = int(os.getenv("CACHE_TTL", "300"))  # 5 minutes default

    # OTP configuration
    OTP_LENGTH: int = int(os.getenv("OTP_LENGTH", "6"))  # OTP code length
    OTP_EXPIRE_MINUTES: int = int(os.getenv("OTP_EXPIRE_MINUTES", "5"))  # OTP expiration time
    OTP_MAX_ATTEMPTS: int = int(os.getenv("OTP_MAX_ATTEMPTS", "3"))  # Max verification attempts
    OTP_RATE_LIMIT_MINUTES: int = int(os.getenv("OTP_RATE_LIMIT_MINUTES", "1"))  # Rate limit window
    OTP_MAX_REQUESTS_PER_WINDOW: int = int(os.getenv("OTP_MAX_REQUESTS_PER_WINDOW", "3"))  # Max requests per window

    # OTP Delivery System Configuration
    OTP_DELIVERY_ENABLED: bool = os.getenv("OTP_DELIVERY_ENABLED", "true").lower() == "true"
    OTP_DEFAULT_CHANNELS: str = os.getenv("OTP_DEFAULT_CHANNELS", "email,sms")
    OTP_MAX_DELIVERY_ATTEMPTS: int = int(os.getenv("OTP_MAX_DELIVERY_ATTEMPTS", "3"))
    OTP_DELIVERY_RETRY_DELAY: int = int(os.getenv("OTP_DELIVERY_RETRY_DELAY", "300"))  # 5 minutes
    OTP_DELIVERY_TIMEOUT: int = int(os.getenv("OTP_DELIVERY_TIMEOUT", "30"))  # 30 seconds

    # SMS Gateway Configuration
    SMS_GATEWAY_TYPE: str = os.getenv("SMS_GATEWAY_TYPE", "gammu")  # gammu, kannel, android
    SMS_GATEWAY_CONFIG_FILE: str = os.getenv("SMS_GATEWAY_CONFIG_FILE", "/etc/gammurc")
    SMS_GATEWAY_URL: str = os.getenv("SMS_GATEWAY_URL", "")  # For Android/Kannel
    SMS_GATEWAY_API_KEY: str = os.getenv("SMS_GATEWAY_API_KEY", "")
    SMS_ENABLED: bool = os.getenv("SMS_ENABLED", "true").lower() == "true"

    # Celery Configuration
    CELERY_BROKER_URL: str = os.getenv("CELERY_BROKER_URL", REDIS_URL)
    CELERY_RESULT_BACKEND: str = os.getenv("CELERY_RESULT_BACKEND", REDIS_URL)
    CELERY_TASK_SERIALIZER: str = os.getenv("CELERY_TASK_SERIALIZER", "json")
    CELERY_ACCEPT_CONTENT: list = os.getenv("CELERY_ACCEPT_CONTENT", "json").split(",")
    CELERY_RESULT_SERIALIZER: str = os.getenv("CELERY_RESULT_SERIALIZER", "json")
    CELERY_TIMEZONE: str = os.getenv("CELERY_TIMEZONE", "UTC")

    # Monitoring Configuration
    PROMETHEUS_ENABLED: bool = os.getenv("PROMETHEUS_ENABLED", "true").lower() == "true"
    FLOWER_ENABLED: bool = os.getenv("FLOWER_ENABLED", "true").lower() == "true"
    FLOWER_PORT: int = int(os.getenv("FLOWER_PORT", "5555"))

    model_config = {
        "case_sensitive": True,
        "env_file": ".env"
    }

settings = Settings()

class VaultConfig:
    @staticmethod
    def _read_key_file(path: str) -> str:
        try:
            with open(path, "r") as f:
                return f.read().replace('\r\n', '\n')
        except FileNotFoundError:
            return ""

# Initialize the keys after the class is defined
VaultConfig.PRIVATE_KEY = VaultConfig._read_key_file("keys/private.pem")
VaultConfig.PUBLIC_KEY = VaultConfig._read_key_file("keys/public.pem")
