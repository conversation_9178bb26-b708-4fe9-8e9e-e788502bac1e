# Zionix Backend Environment Configuration
# Copy this file to .env and update with your actual values

# ===========================================
# GOOGLE EMAIL OTP CONFIGURATION
# ===========================================

# Gmail Configuration for OTP Email Sending
# IMPORTANT: Use Gmail App Password, not your regular password!
# Setup Guide: https://support.google.com/accounts/answer/185833
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-16-char-app-password
MAIL_FROM=<EMAIL>
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_FROM_NAME=Zionix Auth Service

# OTP Security Configuration (Optional - defaults are secure)
OTP_LENGTH=6                          # OTP code length (4-12 digits)
OTP_EXPIRE_MINUTES=5                  # OTP expiration time in minutes
OTP_MAX_ATTEMPTS=3                    # Maximum verification attempts
OTP_RATE_LIMIT_MINUTES=1              # Rate limit window in minutes
OTP_MAX_REQUESTS_PER_WINDOW=3         # Max OTP requests per window

# ===========================================
# ENCRYPTION KEYS (Already configured)
# ===========================================
VAULT_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQChX8d4f7Z4G6HV\n...<rest of private key>...\n-----END PRIVATE KEY-----"
VAULT_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoV/HeH+2eBuh1...<rest of public key>...\n-----END PUBLIC KEY-----"

# ===========================================
# SETUP INSTRUCTIONS
# ===========================================

# 1. Gmail App Password Setup:
#    - Go to https://myaccount.google.com/security
#    - Enable 2-Step Verification
#    - Go to https://myaccount.google.com/apppasswords
#    - Generate app password for "Mail" application
#    - Use the 16-character password as MAIL_PASSWORD

# 2. Update Configuration:
#    - Replace MAIL_USERNAME with your Gmail address
#    - Replace MAIL_PASSWORD with your Gmail app password
#    - Optionally customize MAIL_FROM and MAIL_FROM_NAME

# 3. Test the Setup:
#    - Run: start_otp_services.bat
#    - Open: test_enhanced_otp_api.html
#    - Follow the 3-step OTP process

# 4. Security Notes:
#    - Never commit real credentials to version control
#    - Use environment-specific .env files
#    - Rotate app passwords regularly
#    - Monitor email sending logs
