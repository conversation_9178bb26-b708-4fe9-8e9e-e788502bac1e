# 🔐 Google Email OTP Setup Guide for Auth-Service

## 📋 Overview
This guide will help you set up and test the Google Email OTP functionality for password reset in the auth-service.

## 🚀 Quick Setup

### Step 1: Gmail App Password Setup

1. **Enable 2-Factor Authentication on Gmail:**
   - Go to [Google Account Security](https://myaccount.google.com/security)
   - Enable 2-Step Verification if not already enabled

2. **Generate App Password:**
   - Go to [App Passwords](https://myaccount.google.com/apppasswords)
   - Select "Mail" and "Other (Custom name)"
   - Enter "Zionix Auth Service"
   - Copy the 16-character app password

### Step 2: Configure Environment Variables

Create a `.env` file in the root directory or update your existing one:

```env
# Gmail Configuration for OTP
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-16-char-app-password
MAIL_FROM=<EMAIL>
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_FROM_NAME=Zionix Auth Service

# OTP Configuration (Optional - defaults are good)
OTP_LENGTH=6
OTP_EXPIRE_MINUTES=5
OTP_MAX_ATTEMPTS=3
OTP_RATE_LIMIT_MINUTES=1
OTP_MAX_REQUESTS_PER_WINDOW=3
```

### Step 3: Start the Services

```bash
# Start all services with the hybrid gateway setup
docker-compose -f docker-compose.hybrid.yml up -d

# Check if auth-service is running
docker-compose -f docker-compose.hybrid.yml ps auth-service

# Check auth-service logs for email configuration
docker-compose -f docker-compose.hybrid.yml logs auth-service
```

## 🧪 Testing the OTP Functionality

### Method 1: Using the Web Test Interface

1. **Open the test page:**
   ```
   file:///c:/Users/<USER>/Documents/GitHub/zionix-be-v1/test_enhanced_otp_api.html
   ```

2. **Follow the 3-step process:**
   - Enter your Gmail address
   - Check your email for the OTP
   - Verify the OTP and reset password

### Method 2: Using PowerShell/cURL

1. **Create a test user first:**
   ```powershell
   Invoke-RestMethod -Uri 'http://localhost:8002/api/v1/users/' -Method POST -ContentType 'application/json' -Body '{"email":"<EMAIL>","first_name":"Test","last_name":"User","password":"OldPassword123!","confirm_password":"OldPassword123!"}'
   ```

2. **Request OTP:**
   ```powershell
   Invoke-RestMethod -Uri 'http://localhost:8002/api/v1/forgot-password' -Method POST -ContentType 'application/json' -Body '{"email":"<EMAIL>"}'
   ```

3. **Check your email and verify OTP:**
   ```powershell
   Invoke-RestMethod -Uri 'http://localhost:8002/api/v1/verify-otp' -Method POST -ContentType 'application/json' -Body '{"email":"<EMAIL>","otp_code":"123456"}'
   ```

4. **Reset password:**
   ```powershell
   Invoke-RestMethod -Uri 'http://localhost:8002/api/v1/reset-password' -Method POST -ContentType 'application/json' -Body '{"email":"<EMAIL>","otp_code":"123456","new_password":"NewPassword123!","confirm_password":"NewPassword123!"}'
   ```

### Method 3: Using the Windows Batch Script

```bash
# Run the automated test script
scripts\windows\test_password_reset.bat
```

## 📧 Email Template Preview

The OTP email includes:
- **Professional branding** with Zionix logo
- **Security warnings** about not sharing the code
- **Clear expiration time** (5 minutes)
- **Step-by-step instructions**
- **Security tips** and best practices

## 🔍 Troubleshooting

### Common Issues:

1. **"Failed to send email" error:**
   - Check Gmail app password is correct
   - Verify 2FA is enabled on Gmail
   - Check MAIL_USERNAME and MAIL_PASSWORD in environment

2. **"Service not running" error:**
   - Ensure Docker services are up: `docker-compose -f docker-compose.hybrid.yml ps`
   - Check auth-service logs: `docker-compose -f docker-compose.hybrid.yml logs auth-service`

3. **"Rate limit exceeded" error:**
   - Wait 1 minute between OTP requests
   - This is a security feature to prevent abuse

4. **"Invalid or expired OTP" error:**
   - OTP expires in 5 minutes
   - Each OTP can only be used once
   - Check for typos in the 6-digit code

### Debug Commands:

```bash
# Check if email configuration is loaded
docker-compose -f docker-compose.hybrid.yml exec auth-service python -c "from app.core.config import settings; print(f'Mail server: {settings.MAIL_SERVER}, Username: {settings.MAIL_USERNAME}')"

# Check database for OTP records
docker-compose -f docker-compose.hybrid.yml exec postgres-auth psql -U postgres -d auth_service -c "SELECT email, otp_code, is_used, expires_at FROM otps ORDER BY created_at DESC LIMIT 5;"

# Test email connectivity
docker-compose -f docker-compose.hybrid.yml exec auth-service python -c "import smtplib; print('SMTP test passed')"
```

## 🛡️ Security Features

- **Rate Limiting:** 3 requests per minute per user
- **Expiration:** 5-minute OTP lifetime
- **One-time Use:** OTPs marked as used after successful reset
- **Email Obfuscation:** Errors don't reveal if email exists
- **Secure Generation:** Cryptographically secure random codes
- **Audit Logging:** All OTP events are logged

## 📱 Frontend Integration

The OTP functionality is already integrated into the frontend auth system:

- **Login page:** Includes "Forgot Password" link
- **JavaScript functions:** Ready for OTP workflow
- **Error handling:** User-friendly messages
- **Progress tracking:** Step-by-step UI

## 🎯 Next Steps

1. **Configure your Gmail credentials** in the environment variables
2. **Test with your own email** using the web interface
3. **Integrate with your frontend** application
4. **Monitor logs** for any issues
5. **Customize email templates** if needed

The system is production-ready with enterprise-grade security features!
