@echo off
echo 🚀 Starting Zionix Services with Google Email OTP
echo ================================================

echo.
echo 1. Stopping any existing services...
echo.
docker-compose -f docker-compose.hybrid.yml down

echo.
echo 2. Building and starting services...
echo.
docker-compose -f docker-compose.hybrid.yml up -d --build

echo.
echo 3. Waiting for services to start (30 seconds)...
echo.
timeout /t 30 /nobreak

echo.
echo 4. Checking service status...
echo.

echo Checking auth-service health...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:8002/health' -Method GET -TimeoutSec 10; Write-Host '✅ Auth-service is running!' -ForegroundColor Green } catch { Write-Host '❌ Auth-service not ready yet. Check logs with: docker-compose -f docker-compose.hybrid.yml logs auth-service' -ForegroundColor Red }"

echo.
echo Checking admin-service health...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://localhost:8001/health' -Method GET -TimeoutSec 10; Write-Host '✅ Admin-service is running!' -ForegroundColor Green } catch { Write-Host '❌ Admin-service not ready yet. Check logs with: docker-compose -f docker-compose.hybrid.yml logs admin-service' -ForegroundColor Red }"

echo.
echo 5. Service URLs:
echo ===============
echo 🔐 Auth Service API: http://localhost:8002/docs
echo 👥 Admin Service API: http://localhost:8001/docs
echo 🌐 APISIX Gateway: http://localhost:9080
echo 🔀 Envoy Gateway: http://localhost:10000
echo 🗄️ PgAdmin: http://localhost:5050
echo.

echo 6. Testing OTP functionality:
echo =============================
echo 📧 Test OTP with web interface: test_enhanced_otp_api.html
echo 🧪 Run OTP status check: test_otp_status.bat
echo 📖 Setup guide: GOOGLE_EMAIL_OTP_SETUP_GUIDE.md
echo.

echo ⚠️  IMPORTANT: Configure your Gmail credentials in .env file before testing!
echo    See GOOGLE_EMAIL_OTP_SETUP_GUIDE.md for details.
echo.

pause
