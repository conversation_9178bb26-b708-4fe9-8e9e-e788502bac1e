"""
Enhanced email templates for OTP and password reset notifications
with improved styling and security warnings.
"""

from datetime import datetime, timezone
from app.core.config import settings

def get_base_email_style() -> str:
    """Get base CSS styles for email templates"""
    return """
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333333;
            max-width: 600px;
            margin: 0 auto;
            padding: 0;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin: 20px;
        }
        .header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        .content {
            padding: 40px 30px;
        }
        .otp-container {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            font-size: 32px;
            font-weight: bold;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
            margin: 30px 0;
            letter-spacing: 8px;
            font-family: 'Courier New', monospace;
            border: 3px solid #0056b3;
        }
        .security-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-left: 4px solid #f39c12;
            color: #856404;
            padding: 20px;
            border-radius: 6px;
            margin: 25px 0;
        }
        .security-warning h3 {
            margin: 0 0 10px 0;
            color: #d68910;
            font-size: 16px;
        }
        .security-warning ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .security-warning li {
            margin: 8px 0;
        }
        .success-box {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-left: 4px solid #28a745;
            color: #155724;
            padding: 20px;
            border-radius: 6px;
            margin: 25px 0;
        }
        .info-box {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 4px solid #17a2b8;
            color: #0c5460;
            padding: 20px;
            border-radius: 6px;
            margin: 25px 0;
        }
        .footer {
            background-color: #f8f9fa;
            color: #6c757d;
            padding: 20px 30px;
            text-align: center;
            font-size: 14px;
            border-top: 1px solid #dee2e6;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        .timestamp {
            color: #6c757d;
            font-size: 12px;
            margin-top: 20px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
        }
    </style>
    """

def get_otp_email_template(otp_code: str, user_name: str, expires_minutes: int = None) -> tuple[str, str]:
    """
    Generate enhanced OTP email template with improved security warnings.
    
    Returns:
        tuple: (html_content, text_content)
    """
    if expires_minutes is None:
        expires_minutes = settings.OTP_EXPIRE_MINUTES
    
    current_time = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
    
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset OTP - Zionix</title>
        {get_base_email_style()}
    </head>
    <body>
        <div class="email-container">
            <div class="header">
                <div class="logo">🔐 Zionix</div>
                <h1>Password Reset Request</h1>
            </div>
            
            <div class="content">
                <p>Hello <strong>{user_name}</strong>,</p>
                
                <p>We received a request to reset your password. To proceed with your password reset, please use the following One-Time Password (OTP):</p>
                
                <div class="otp-container">
                    {otp_code}
                </div>
                
                <div class="security-warning">
                    <h3>🛡️ Security Information</h3>
                    <ul>
                        <li><strong>This OTP expires in {expires_minutes} minutes</strong></li>
                        <li>Never share this code with anyone, including Zionix support</li>
                        <li>We will never ask for your OTP via phone or email</li>
                        <li>If you didn't request this reset, please ignore this email</li>
                        <li>Consider changing your password if you suspect unauthorized access</li>
                    </ul>
                </div>
                
                <div class="info-box">
                    <p><strong>What happens next?</strong></p>
                    <ol>
                        <li>Enter this OTP on the password reset page</li>
                        <li>Create a new, strong password</li>
                        <li>You'll be able to log in with your new password</li>
                    </ol>
                </div>
                
                <p>If you're having trouble with the password reset process, please contact our support team.</p>
                
                <p>Best regards,<br>
                <strong>The Zionix Security Team</strong></p>
                
                <div class="timestamp">
                    Request received: {current_time}
                </div>
            </div>
            
            <div class="footer">
                <p>This is an automated security message. Please do not reply to this email.</p>
                <p>© 2024 Zionix. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    """

    text_content = f"""
    🔐 ZIONIX - PASSWORD RESET REQUEST
    
    Hello {user_name},
    
    We received a request to reset your password. To proceed with your password reset, please use the following One-Time Password (OTP):
    
    ═══════════════════════════════════
    OTP CODE: {otp_code}
    ═══════════════════════════════════
    
    🛡️ SECURITY INFORMATION:
    • This OTP expires in {expires_minutes} minutes
    • Never share this code with anyone, including Zionix support
    • We will never ask for your OTP via phone or email
    • If you didn't request this reset, please ignore this email
    • Consider changing your password if you suspect unauthorized access
    
    WHAT HAPPENS NEXT:
    1. Enter this OTP on the password reset page
    2. Create a new, strong password
    3. You'll be able to log in with your new password
    
    If you're having trouble with the password reset process, please contact our support team.
    
    Best regards,
    The Zionix Security Team
    
    Request received: {current_time}
    
    This is an automated security message. Please do not reply to this email.
    © 2024 Zionix. All rights reserved.
    """
    
    return html_content, text_content

def get_password_reset_confirmation_template(user_name: str) -> tuple[str, str]:
    """
    Generate enhanced password reset confirmation email template.
    
    Returns:
        tuple: (html_content, text_content)
    """
    current_time = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")
    
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset Successful - Zionix</title>
        {get_base_email_style()}
    </head>
    <body>
        <div class="email-container">
            <div class="header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="logo">✅ Zionix</div>
                <h1>Password Reset Successful</h1>
            </div>
            
            <div class="content">
                <p>Hello <strong>{user_name}</strong>,</p>
                
                <div class="success-box">
                    <h3>🎉 Success!</h3>
                    <p>Your password has been reset successfully. You can now log in with your new password.</p>
                </div>
                
                <div class="security-warning">
                    <h3>🛡️ Security Recommendations</h3>
                    <ul>
                        <li>Use a strong, unique password for your account</li>
                        <li>Enable two-factor authentication if available</li>
                        <li>Never share your password with anyone</li>
                        <li>Log out from shared or public devices</li>
                        <li>Monitor your account for any suspicious activity</li>
                    </ul>
                </div>
                
                <div class="info-box">
                    <p><strong>⚠️ Didn't make this change?</strong></p>
                    <p>If you did not reset your password, please contact our support team immediately. Your account security may be compromised.</p>
                </div>
                
                <p>Thank you for keeping your account secure!</p>
                
                <p>Best regards,<br>
                <strong>The Zionix Security Team</strong></p>
                
                <div class="timestamp">
                    Password reset completed: {current_time}
                </div>
            </div>
            
            <div class="footer">
                <p>This is an automated security notification. Please do not reply to this email.</p>
                <p>© 2024 Zionix. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    """

    text_content = f"""
    ✅ ZIONIX - PASSWORD RESET SUCCESSFUL
    
    Hello {user_name},
    
    🎉 SUCCESS!
    Your password has been reset successfully. You can now log in with your new password.
    
    🛡️ SECURITY RECOMMENDATIONS:
    • Use a strong, unique password for your account
    • Enable two-factor authentication if available
    • Never share your password with anyone
    • Log out from shared or public devices
    • Monitor your account for any suspicious activity
    
    ⚠️ DIDN'T MAKE THIS CHANGE?
    If you did not reset your password, please contact our support team immediately. Your account security may be compromised.
    
    Thank you for keeping your account secure!
    
    Best regards,
    The Zionix Security Team
    
    Password reset completed: {current_time}
    
    This is an automated security notification. Please do not reply to this email.
    © 2024 Zionix. All rights reserved.
    """
    
    return html_content, text_content

def get_email_verification_template(otp_code: str, user_name: str, expires_minutes: int = None) -> tuple[str, str]:
    """
    Generate email verification OTP template for new user registration.

    Returns:
        tuple: (html_content, text_content)
    """
    if expires_minutes is None:
        expires_minutes = settings.OTP_EXPIRE_MINUTES

    current_time = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S UTC")

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification - Zionix</title>
        {EMAIL_STYLES}
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🔐 ZIONIX</div>
                <h1>Welcome! Please Verify Your Email</h1>
            </div>

            <div class="content">
                <p>Hello <strong>{user_name}</strong>,</p>

                <p>Welcome to Zionix! To complete your registration and activate your account, please verify your email address using the One-Time Password (OTP) below:</p>

                <div class="otp-box">
                    <div class="otp-label">Your Verification Code:</div>
                    <div class="otp-code">{otp_code}</div>
                    <div class="otp-expiry">⏰ Expires in {expires_minutes} minutes</div>
                </div>

                <div class="warning-box">
                    <p><strong>🛡️ Security Information:</strong></p>
                    <ul>
                        <li>This code expires in <strong>{expires_minutes} minutes</strong></li>
                        <li>Never share this code with anyone</li>
                        <li>We will never ask for your verification code via phone</li>
                        <li>If you didn't create this account, please ignore this email</li>
                    </ul>
                </div>

                <div class="info-box">
                    <p><strong>What happens next?</strong></p>
                    <ol>
                        <li>Enter this verification code on the registration page</li>
                        <li>Your account will be activated immediately</li>
                        <li>You'll be able to log in and access all features</li>
                    </ol>
                </div>

                <p>If you're having trouble with the verification process, please contact our support team.</p>

                <p>Best regards,<br>
                <strong>The Zionix Team</strong></p>

                <div class="timestamp">
                    Registration started: {current_time}
                </div>
            </div>

            <div class="footer">
                <p>This is an automated verification message. Please do not reply to this email.</p>
                <p>© 2024 Zionix. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    """

    text_content = f"""
    🔐 ZIONIX - EMAIL VERIFICATION REQUIRED

    Hello {user_name},

    Welcome to Zionix! To complete your registration and activate your account, please verify your email address using the following One-Time Password (OTP):

    ═══════════════════════════════════
    VERIFICATION CODE: {otp_code}
    ═══════════════════════════════════

    🛡️ SECURITY INFORMATION:
    • This code expires in {expires_minutes} minutes
    • Never share this code with anyone
    • We will never ask for your verification code via phone
    • If you didn't create this account, please ignore this email

    WHAT HAPPENS NEXT:
    1. Enter this verification code on the registration page
    2. Your account will be activated immediately
    3. You'll be able to log in and access all features

    If you're having trouble with the verification process, please contact our support team.

    Best regards,
    The Zionix Team

    Registration started: {current_time}

    This is an automated verification message. Please do not reply to this email.
    © 2024 Zionix. All rights reserved.
    """

    return html_content, text_content
