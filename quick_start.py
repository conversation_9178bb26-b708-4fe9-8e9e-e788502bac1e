#!/usr/bin/env python3
"""
Quick Start Script for OTP Delivery System
"""

import subprocess
import time
import sys
import os

def run_command(command, description, timeout=60):
    """Run a command and return the result"""
    print(f"\n🔧 {description}...")
    print(f"Command: {command}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=os.getcwd()
        )
        
        if result.returncode == 0:
            print(f"✅ {description} - SUCCESS")
            if result.stdout:
                print(f"Output: {result.stdout[:500]}...")
            return True
        else:
            print(f"❌ {description} - FAILED")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - TIMEOUT")
        return False
    except Exception as e:
        print(f"❌ {description} - ERROR: {e}")
        return False

def main():
    print("🚀 Starting OTP Delivery System")
    print("=" * 50)
    
    # Step 1: Check Docker
    if not run_command("docker --version", "Checking Docker", 10):
        print("❌ Docker is not available. Please install Docker first.")
        return False
    
    # Step 2: Check Docker Compose
    if not run_command("docker-compose --version", "Checking Docker Compose", 10):
        print("❌ Docker Compose is not available.")
        return False
    
    # Step 3: Stop existing services
    run_command("docker-compose -f docker-compose.hybrid.yml down", "Stopping existing services", 60)
    
    # Step 4: Build auth service
    if not run_command("docker-compose -f docker-compose.hybrid.yml build auth-service", "Building auth service", 300):
        print("❌ Failed to build auth service")
        return False
    
    # Step 5: Start services
    if not run_command("docker-compose -f docker-compose.hybrid.yml up -d", "Starting services", 120):
        print("❌ Failed to start services")
        return False
    
    # Step 6: Wait for services to start
    print("\n⏳ Waiting for services to start...")
    time.sleep(30)
    
    # Step 7: Check service status
    run_command("docker-compose -f docker-compose.hybrid.yml ps", "Checking service status", 30)
    
    # Step 8: Run migration
    print("\n🗄️ Running database migration...")
    run_command("docker-compose -f docker-compose.hybrid.yml exec -T auth-service alembic upgrade head", "Database migration", 60)
    
    print("\n🎉 OTP Delivery System Started!")
    print("\n📊 Access Points:")
    print("- Auth Service: http://localhost:8002")
    print("- Admin Service: http://localhost:8001")
    print("- Flower (Celery Monitor): http://localhost:5555")
    print("- PgAdmin: http://localhost:5050")
    
    print("\n🧪 To test the system:")
    print("python test_otp_delivery_system.py")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Startup failed. Check the errors above.")
        sys.exit(1)
    else:
        print("\n✅ Startup completed successfully!")
        sys.exit(0)
