from sqlalchemy import Column, Integer, String, DateTime, <PERSON>olean, Foreign<PERSON>ey, Text, Float
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.db.session import Base

class OTP(Base):
    __tablename__ = "otps"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    otp_code = Column(String(6), nullable=False)
    purpose = Column(String(50), nullable=False)  # 'password_reset', 'email_verification', etc.
    is_used = Column(Boolean, default=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Enhanced delivery tracking fields
    delivery_status = Column(String(20), default="pending")  # pending, queued, sending, delivered, failed
    delivery_channel = Column(String(10))  # email, sms, push
    delivery_attempts = Column(Integer, default=0)
    last_delivery_attempt = Column(DateTime(timezone=True))
    delivery_error = Column(Text)  # Store error details
    delivery_id = Column(String(100))  # External delivery ID from gateway
    delivery_cost = Column(Float, default=0.0)  # Track cost per delivery

    # Relationship
    user = relationship("User", back_populates="otps")

    def __repr__(self):
        return f"<OTP {self.otp_code} for user {self.user_id} via {self.delivery_channel}>"