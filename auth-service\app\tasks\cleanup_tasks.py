"""
Cleanup and Maintenance Tasks
Periodic tasks for system maintenance and cleanup
"""

import logging
from datetime import datetime, timedelta, timezone
from sqlalchemy import and_, or_

from app.core.celery_app import celery_app
from app.db.session import get_db
from app.models.otp import OTP
from app.crud.otp import cleanup_expired_otps

logger = logging.getLogger(__name__)

@celery_app.task
def cleanup_expired_otps():
    """
    Clean up expired OTPs from the database
    Runs every 5 minutes via Celery Beat
    """
    try:
        db = next(get_db())
        
        # Use existing cleanup function
        cleanup_expired_otps(db)
        
        # Also clean up old delivery logs (older than 30 days)
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
        old_otps = db.query(OTP).filter(
            and_(
                OTP.created_at < cutoff_date,
                or_(
                    OTP.delivery_status == 'delivered',
                    OTP.delivery_status == 'failed'
                )
            )
        ).count()
        
        if old_otps > 0:
            db.query(OTP).filter(
                and_(
                    OTP.created_at < cutoff_date,
                    or_(
                        OTP.delivery_status == 'delivered',
                        OTP.delivery_status == 'failed'
                    )
                )
            ).delete()
            db.commit()
            logger.info(f"Cleaned up {old_otps} old OTP records")
        
        return {
            'cleaned_expired': True,
            'cleaned_old_records': old_otps
        }
        
    except Exception as e:
        logger.error(f"OTP cleanup failed: {str(e)}")
        return {'error': str(e)}

@celery_app.task
def retry_failed_deliveries():
    """
    Retry failed OTP deliveries that are eligible for retry
    Runs every 10 minutes via Celery Beat
    """
    try:
        db = next(get_db())
        
        # Find failed deliveries that can be retried
        retry_window = datetime.now(timezone.utc) - timedelta(minutes=30)
        
        failed_otps = db.query(OTP).filter(
            and_(
                OTP.delivery_status == 'failed',
                OTP.delivery_attempts < 3,  # Max 3 attempts
                OTP.last_delivery_attempt < retry_window,  # Wait 30 minutes between retries
                OTP.expires_at > datetime.now(timezone.utc)  # Still valid
            )
        ).limit(50).all()  # Limit to 50 retries per run
        
        retry_count = 0
        for otp in failed_otps:
            try:
                # Reset status for retry
                otp.delivery_status = 'pending'
                otp.delivery_error = None
                db.commit()
                
                # Queue for delivery
                from app.core.otp_delivery import otp_delivery_service, DeliveryRequest, DeliveryChannel
                from app.models.user import User
                
                user = db.query(User).filter(User.id == otp.user_id).first()
                if user:
                    delivery_request = DeliveryRequest(
                        otp_id=otp.id,
                        user_id=otp.user_id,
                        recipient_email=user.email,
                        recipient_phone=getattr(user, 'phone', None),
                        channels=[DeliveryChannel.EMAIL, DeliveryChannel.SMS],
                        priority=3,  # Low priority for retries
                        max_retries=1  # Only one more retry
                    )
                    
                    import asyncio
                    success = asyncio.run(otp_delivery_service.queue_delivery(delivery_request))
                    if success:
                        retry_count += 1
                        logger.info(f"Queued retry for OTP {otp.id}")
                
            except Exception as e:
                logger.error(f"Failed to retry OTP {otp.id}: {str(e)}")
        
        return {
            'found_failed': len(failed_otps),
            'queued_retries': retry_count
        }
        
    except Exception as e:
        logger.error(f"Retry failed deliveries task failed: {str(e)}")
        return {'error': str(e)}

@celery_app.task
def update_delivery_stats():
    """
    Update delivery statistics and metrics
    Runs every 15 minutes via Celery Beat
    """
    try:
        db = next(get_db())
        
        # Calculate stats for the last 24 hours
        start_time = datetime.now(timezone.utc) - timedelta(hours=24)
        
        from sqlalchemy import func
        
        # Overall stats
        total_otps = db.query(func.count(OTP.id)).filter(
            OTP.created_at >= start_time
        ).scalar() or 0
        
        delivered_otps = db.query(func.count(OTP.id)).filter(
            and_(
                OTP.created_at >= start_time,
                OTP.delivery_status == 'delivered'
            )
        ).scalar() or 0
        
        failed_otps = db.query(func.count(OTP.id)).filter(
            and_(
                OTP.created_at >= start_time,
                OTP.delivery_status == 'failed'
            )
        ).scalar() or 0
        
        # Channel-specific stats
        channel_stats = db.query(
            OTP.delivery_channel,
            func.count(OTP.id).label('total'),
            func.sum(func.case([(OTP.delivery_status == 'delivered', 1)], else_=0)).label('delivered'),
            func.avg(func.extract('epoch', OTP.last_delivery_attempt - OTP.created_at)).label('avg_time')
        ).filter(
            and_(
                OTP.created_at >= start_time,
                OTP.delivery_channel.isnot(None)
            )
        ).group_by(OTP.delivery_channel).all()
        
        stats = {
            'period': '24h',
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'overall': {
                'total_otps': total_otps,
                'delivered': delivered_otps,
                'failed': failed_otps,
                'success_rate': (delivered_otps / total_otps * 100) if total_otps > 0 else 0
            },
            'by_channel': {}
        }
        
        for channel_stat in channel_stats:
            channel = channel_stat.delivery_channel
            total = channel_stat.total or 0
            delivered = channel_stat.delivered or 0
            
            stats['by_channel'][channel] = {
                'total': total,
                'delivered': delivered,
                'success_rate': (delivered / total * 100) if total > 0 else 0,
                'avg_delivery_time': channel_stat.avg_time or 0
            }
        
        logger.info(f"Updated delivery stats: {stats}")
        
        # Here you could store stats in a metrics database,
        # send to monitoring systems, create alerts, etc.
        
        return stats
        
    except Exception as e:
        logger.error(f"Stats update failed: {str(e)}")
        return {'error': str(e)}

@celery_app.task
def health_check_gateways():
    """
    Health check for SMS and email gateways
    """
    try:
        health_status = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'gateways': {}
        }
        
        # Check SMS gateway
        try:
            from app.core.sms_gateway import sms_gateway_manager
            # Simple health check - try to get balance or status
            import asyncio
            
            if sms_gateway_manager.gateways:
                for gateway in sms_gateway_manager.gateways:
                    gateway_name = type(gateway).__name__
                    try:
                        balance = asyncio.run(gateway.get_balance())
                        health_status['gateways'][gateway_name] = {
                            'status': 'healthy',
                            'balance': balance
                        }
                    except Exception as e:
                        health_status['gateways'][gateway_name] = {
                            'status': 'unhealthy',
                            'error': str(e)
                        }
            else:
                health_status['gateways']['sms'] = {
                    'status': 'not_configured'
                }
                
        except Exception as e:
            health_status['gateways']['sms'] = {
                'status': 'error',
                'error': str(e)
            }
        
        # Check email gateway (basic SMTP connectivity)
        try:
            health_status['gateways']['email'] = {
                'status': 'healthy',  # Assume healthy if no errors
                'note': 'Basic email service available'
            }
        except Exception as e:
            health_status['gateways']['email'] = {
                'status': 'unhealthy',
                'error': str(e)
            }
        
        logger.info(f"Gateway health check: {health_status}")
        return health_status
        
    except Exception as e:
        logger.error(f"Gateway health check failed: {str(e)}")
        return {'error': str(e)}

@celery_app.task
def generate_delivery_report():
    """
    Generate daily delivery report
    """
    try:
        db = next(get_db())
        
        # Generate report for yesterday
        yesterday = datetime.now(timezone.utc).date() - timedelta(days=1)
        start_time = datetime.combine(yesterday, datetime.min.time()).replace(tzinfo=timezone.utc)
        end_time = start_time + timedelta(days=1)
        
        from sqlalchemy import func
        
        # Get comprehensive stats
        report_data = db.query(
            OTP.delivery_channel,
            OTP.delivery_status,
            OTP.purpose,
            func.count(OTP.id).label('count'),
            func.sum(OTP.delivery_cost).label('total_cost'),
            func.avg(func.extract('epoch', OTP.last_delivery_attempt - OTP.created_at)).label('avg_time')
        ).filter(
            and_(
                OTP.created_at >= start_time,
                OTP.created_at < end_time
            )
        ).group_by(
            OTP.delivery_channel, OTP.delivery_status, OTP.purpose
        ).all()
        
        report = {
            'date': yesterday.isoformat(),
            'generated_at': datetime.now(timezone.utc).isoformat(),
            'summary': {},
            'details': []
        }
        
        total_count = 0
        total_cost = 0.0
        
        for row in report_data:
            detail = {
                'channel': row.delivery_channel or 'unknown',
                'status': row.delivery_status or 'unknown',
                'purpose': row.purpose,
                'count': row.count,
                'cost': row.total_cost or 0.0,
                'avg_delivery_time': row.avg_time or 0.0
            }
            report['details'].append(detail)
            
            total_count += row.count
            total_cost += (row.total_cost or 0.0)
        
        report['summary'] = {
            'total_otps': total_count,
            'total_cost': total_cost,
            'avg_cost_per_otp': (total_cost / total_count) if total_count > 0 else 0
        }
        
        logger.info(f"Generated daily report for {yesterday}: {report['summary']}")
        
        # Here you could email the report, store it, etc.
        
        return report
        
    except Exception as e:
        logger.error(f"Report generation failed: {str(e)}")
        return {'error': str(e)}
