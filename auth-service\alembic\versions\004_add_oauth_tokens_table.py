"""Add OAuth tokens table

Revision ID: 004
Revises: 003
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '004'
down_revision = '003'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create oauth_tokens table
    op.create_table('oauth_tokens',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('provider', sa.String(length=50), nullable=False),
        sa.Column('access_token', sa.Text(), nullable=False),
        sa.Column('refresh_token', sa.Text(), nullable=True),
        sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('scope', sa.Text(), nullable=True),
        sa.Column('token_type', sa.String(length=50), nullable=True),
        sa.Column('provider_user_id', sa.String(length=100), nullable=True),
        sa.Column('provider_email', sa.String(length=255), nullable=True),
        sa.Column('provider_name', sa.String(length=255), nullable=True),
        sa.Column('provider_picture', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index(op.f('ix_oauth_tokens_id'), 'oauth_tokens', ['id'], unique=False)
    op.create_index('ix_oauth_tokens_user_provider', 'oauth_tokens', ['user_id', 'provider'], unique=False)
    op.create_index('ix_oauth_tokens_provider_user_id', 'oauth_tokens', ['provider_user_id'], unique=False)


def downgrade() -> None:
    # Drop indexes
    op.drop_index('ix_oauth_tokens_provider_user_id', table_name='oauth_tokens')
    op.drop_index('ix_oauth_tokens_user_provider', table_name='oauth_tokens')
    op.drop_index(op.f('ix_oauth_tokens_id'), table_name='oauth_tokens')
    
    # Drop table
    op.drop_table('oauth_tokens')
