"""
OAuth-related Pydantic schemas
"""

from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime


class GoogleAuthRequest(BaseModel):
    """Request to initiate Google OAuth flow"""
    redirect_uri: Optional[str] = None
    state: Optional[str] = None


class GoogleAuthResponse(BaseModel):
    """Response with Google OAuth authorization URL"""
    authorization_url: str
    state: Optional[str] = None


class GoogleCallbackRequest(BaseModel):
    """Google OAuth callback request"""
    code: str
    state: Optional[str] = None


class GoogleCallbackResponse(BaseModel):
    """Google OAuth callback response"""
    access_token: str
    refresh_token: Optional[str] = None
    expires_in: Optional[int] = None
    scope: Optional[str] = None
    user_info: dict


class OAuthTokenResponse(BaseModel):
    """OAuth token information"""
    id: int
    provider: str
    expires_at: Optional[datetime] = None
    scope: Optional[str] = None
    provider_email: Optional[str] = None
    provider_name: Optional[str] = None
    is_active: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = {"from_attributes": True}


class GmailOTPRequest(BaseModel):
    """Request to send OTP via Gmail API"""
    recipient_email: EmailStr
    user_name: Optional[str] = "User"


class GmailOTPResponse(BaseModel):
    """Response after sending OTP via Gmail API"""
    message: str
    recipient_email: str
    sent_via_gmail: bool = True


class RefreshTokenRequest(BaseModel):
    """Request to refresh OAuth token"""
    provider: str = Field(default="google")


class RefreshTokenResponse(BaseModel):
    """Response after refreshing OAuth token"""
    access_token: str
    expires_in: Optional[int] = None
    message: str


class RevokeTokenRequest(BaseModel):
    """Request to revoke OAuth token"""
    provider: str = Field(default="google")


class RevokeTokenResponse(BaseModel):
    """Response after revoking OAuth token"""
    message: str
    provider: str
    revoked: bool
