@echo off
echo ========================================
echo   OTP Delivery System Startup Script
echo ========================================

echo.
echo 1. Stopping existing services...
docker-compose -f docker-compose.hybrid.yml down

echo.
echo 2. Building auth service with new dependencies...
docker-compose -f docker-compose.hybrid.yml build auth-service

echo.
echo 3. Starting all services...
docker-compose -f docker-compose.hybrid.yml up -d

echo.
echo 4. Waiting for services to start...
timeout /t 30 /nobreak

echo.
echo 5. Checking service status...
docker-compose -f docker-compose.hybrid.yml ps

echo.
echo 6. Running database migration...
docker exec zionix-be-v1-auth-service-1 alembic upgrade head

echo.
echo ========================================
echo   OTP Delivery System Started!
echo ========================================
echo.
echo Services available at:
echo - Auth Service: http://localhost:8002
echo - Admin Service: http://localhost:8001  
echo - Flower (Celery Monitor): http://localhost:5555
echo - PgAdmin: http://localhost:5050
echo.
echo To test the system:
echo   python test_otp_delivery_system.py
echo.
echo To monitor:
echo   docker-compose -f docker-compose.hybrid.yml logs -f
echo.
pause
