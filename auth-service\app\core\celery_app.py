"""
Celery Application Configuration
Centralized Celery configuration for OTP delivery system
"""

import os
from celery import Celery
from app.core.config import settings

# Create Celery instance
celery_app = Celery(
    'zionix_otp_delivery',
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        'app.core.otp_delivery',
        'app.tasks.otp_tasks',
        'app.tasks.cleanup_tasks'
    ]
)

# Configure Celery
celery_app.conf.update(
    # Task serialization
    task_serializer=settings.CELERY_TASK_SERIALIZER,
    accept_content=settings.CELERY_ACCEPT_CONTENT,
    result_serializer=settings.CELERY_RESULT_SERIALIZER,
    timezone=settings.CELERY_TIMEZONE,
    enable_utc=True,
    
    # Task routing
    task_routes={
        'app.core.otp_delivery.deliver_otp_task': {'queue': 'otp_delivery'},
        'app.core.otp_delivery.retry_failed_delivery': {'queue': 'otp_retry'},
        'app.tasks.otp_tasks.*': {'queue': 'otp_delivery'},
        'app.tasks.cleanup_tasks.*': {'queue': 'cleanup'},
    },
    
    # Task execution settings
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_persistent=True,
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'cleanup-expired-otps': {
            'task': 'app.tasks.cleanup_tasks.cleanup_expired_otps',
            'schedule': 300.0,  # Every 5 minutes
        },
        'retry-failed-deliveries': {
            'task': 'app.tasks.cleanup_tasks.retry_failed_deliveries',
            'schedule': 600.0,  # Every 10 minutes
        },
        'update-delivery-stats': {
            'task': 'app.tasks.cleanup_tasks.update_delivery_stats',
            'schedule': 900.0,  # Every 15 minutes
        },
    },
    
    # Worker settings
    worker_hijack_root_logger=False,
    worker_log_color=False,
    
    # Security settings
    worker_disable_rate_limits=False,
    task_reject_on_worker_lost=True,
)

# Optional: Configure Celery for development
if os.getenv('ENVIRONMENT') == 'development':
    celery_app.conf.update(
        task_always_eager=False,  # Set to True for synchronous testing
        task_eager_propagates=True,
    )
