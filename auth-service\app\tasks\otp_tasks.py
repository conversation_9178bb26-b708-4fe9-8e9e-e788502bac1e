"""
OTP Delivery Tasks
Background tasks for OTP delivery and management
"""

import asyncio
import logging
from datetime import datetime, timezone
from typing import Dict, Any

from app.core.celery_app import celery_app
from app.core.otp_delivery import otp_delivery_service, DeliveryRequest, DeliveryChannel
from app.db.session import get_db
from app.models.otp import OTP
from app.models.user import User

logger = logging.getLogger(__name__)

@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def deliver_otp_task(self, otp_id: int, delivery_request_data: Dict[str, Any]):
    """
    Celery task for OTP delivery
    
    Args:
        otp_id: OTP ID to deliver
        delivery_request_data: Serialized DeliveryRequest data
    """
    try:
        # Recreate DeliveryRequest from data
        delivery_request = DeliveryRequest(**delivery_request_data)
        
        # Deliver OTP
        result = asyncio.run(otp_delivery_service.deliver_otp(otp_id))
        
        if not result.success and result.retry_after:
            # Schedule retry if delivery failed but retryable
            logger.warning(f"OTP {otp_id} delivery failed, retrying in {result.retry_after} seconds")
            raise self.retry(countdown=result.retry_after)
        
        # Log result
        if result.success:
            logger.info(f"OTP {otp_id} delivered successfully via {result.channel}")
        else:
            logger.error(f"OTP {otp_id} delivery failed permanently: {result.message}")
        
        return {
            'otp_id': otp_id,
            'success': result.success,
            'channel': result.channel.value if result.channel else None,
            'message': result.message,
            'delivery_id': result.delivery_id
        }
        
    except Exception as e:
        logger.error(f"Celery OTP delivery task failed for OTP {otp_id}: {str(e)}")
        
        # Update OTP status to failed
        try:
            db = next(get_db())
            otp = db.query(OTP).filter(OTP.id == otp_id).first()
            if otp:
                otp.delivery_status = 'failed'
                otp.delivery_error = str(e)
                otp.last_delivery_attempt = datetime.now(timezone.utc)
                db.commit()
        except Exception as db_error:
            logger.error(f"Failed to update OTP status: {str(db_error)}")
        
        raise

@celery_app.task(bind=True, max_retries=2)
def send_bulk_otp_task(self, user_ids: list, purpose: str = "bulk_notification"):
    """
    Send OTP to multiple users (bulk operation)
    
    Args:
        user_ids: List of user IDs
        purpose: OTP purpose
    """
    try:
        db = next(get_db())
        results = []
        
        for user_id in user_ids:
            try:
                user = db.query(User).filter(User.id == user_id).first()
                if not user:
                    continue
                
                # Create OTP
                from app.crud.otp import create_otp
                otp = create_otp(db, user_id=user_id, purpose=purpose)
                
                # Create delivery request
                delivery_request = DeliveryRequest(
                    otp_id=otp.id,
                    user_id=user_id,
                    recipient_email=user.email,
                    recipient_phone=getattr(user, 'phone', None),
                    channels=[DeliveryChannel.EMAIL, DeliveryChannel.SMS],
                    priority=2,  # Normal priority for bulk
                    max_retries=2
                )
                
                # Queue delivery
                success = asyncio.run(otp_delivery_service.queue_delivery(delivery_request))
                results.append({
                    'user_id': user_id,
                    'otp_id': otp.id,
                    'queued': success
                })
                
            except Exception as e:
                logger.error(f"Failed to process bulk OTP for user {user_id}: {str(e)}")
                results.append({
                    'user_id': user_id,
                    'error': str(e)
                })
        
        return {
            'total_users': len(user_ids),
            'processed': len(results),
            'results': results
        }
        
    except Exception as e:
        logger.error(f"Bulk OTP task failed: {str(e)}")
        raise

@celery_app.task
def verify_delivery_status_task(otp_id: int):
    """
    Verify delivery status with external gateway
    
    Args:
        otp_id: OTP ID to check
    """
    try:
        db = next(get_db())
        otp = db.query(OTP).filter(OTP.id == otp_id).first()
        
        if not otp or not otp.delivery_id:
            return {'error': 'OTP not found or no delivery ID'}
        
        # Check status with appropriate gateway
        if otp.delivery_channel == 'sms':
            from app.core.sms_gateway import sms_gateway_manager
            status = asyncio.run(sms_gateway_manager.check_status(otp.delivery_id))
            
            # Update status if changed
            if status.value != otp.delivery_status:
                otp.delivery_status = status.value
                db.commit()
                logger.info(f"Updated OTP {otp_id} status to {status.value}")
        
        return {
            'otp_id': otp_id,
            'status': otp.delivery_status,
            'channel': otp.delivery_channel
        }
        
    except Exception as e:
        logger.error(f"Status verification failed for OTP {otp_id}: {str(e)}")
        return {'error': str(e)}

@celery_app.task
def send_delivery_notification_task(otp_id: int, status: str):
    """
    Send notification about delivery status (webhook, email, etc.)
    
    Args:
        otp_id: OTP ID
        status: Delivery status
    """
    try:
        db = next(get_db())
        otp = db.query(OTP).filter(OTP.id == otp_id).first()
        
        if not otp:
            return {'error': 'OTP not found'}
        
        user = db.query(User).filter(User.id == otp.user_id).first()
        
        # Log delivery notification
        logger.info(f"Delivery notification: OTP {otp_id} for user {user.email} - Status: {status}")
        
        # Here you could send webhooks, admin notifications, etc.
        # For now, we'll just log the event
        
        return {
            'otp_id': otp_id,
            'user_email': user.email,
            'status': status,
            'notified': True
        }
        
    except Exception as e:
        logger.error(f"Delivery notification failed for OTP {otp_id}: {str(e)}")
        return {'error': str(e)}

@celery_app.task
def update_delivery_metrics_task():
    """
    Update delivery metrics and statistics
    """
    try:
        db = next(get_db())
        
        # Calculate metrics for the last hour
        from datetime import timedelta
        start_time = datetime.now(timezone.utc) - timedelta(hours=1)
        
        # Get delivery stats
        from sqlalchemy import func
        stats = db.query(
            OTP.delivery_channel,
            OTP.delivery_status,
            func.count(OTP.id).label('count'),
            func.avg(func.extract('epoch', OTP.last_delivery_attempt - OTP.created_at)).label('avg_time')
        ).filter(
            OTP.created_at >= start_time
        ).group_by(
            OTP.delivery_channel, OTP.delivery_status
        ).all()
        
        metrics = {}
        for stat in stats:
            channel = stat.delivery_channel or 'unknown'
            status = stat.delivery_status or 'unknown'
            
            if channel not in metrics:
                metrics[channel] = {}
            
            metrics[channel][status] = {
                'count': stat.count,
                'avg_delivery_time': stat.avg_time
            }
        
        logger.info(f"Updated delivery metrics: {metrics}")
        
        # Here you could store metrics in a time-series database,
        # send to monitoring systems, etc.
        
        return metrics
        
    except Exception as e:
        logger.error(f"Metrics update failed: {str(e)}")
        return {'error': str(e)}
