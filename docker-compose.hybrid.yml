version: '3.8'

services:
  # APISIX Gateway for Auth Service (Alternative: Nginx-based)
  apisix-gateway:
    image: nginx:alpine
    restart: always
    volumes:
      - ./gateway/apisix/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - auth-service
    ports:
      - "9080:9080"  # APISIX Gateway
    networks:
      - zcare-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:9080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Envoy Gateway for Admin Service
  envoy-gateway:
    image: envoyproxy/envoy:v1.27-latest
    ports:
      - "10000:10000"  # Envoy Gateway
      - "9901:9901"    # Admin interface
    volumes:
      - ./gateway/envoy/envoy.yaml:/etc/envoy/envoy.yaml:ro
    depends_on:
      - admin-service
    networks:
      - zcare-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "pgrep", "envoy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # etcd for APISIX
  etcd:
    image: bitnami/etcd:3.4.15
    restart: always
    volumes:
      - etcd_data:/bitnami/etcd
    environment:
      ETCD_ENABLE_V2: "true"
      ALLOW_NONE_AUTHENTICATION: "yes"
      ETCD_ADVERTISE_CLIENT_URLS: "http://0.0.0.0:2379"
      ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"
    ports:
      - "2379:2380"  # Client port
      - "2380:2381"  # Peer port
    networks:
      - zcare-network

  # Admin Service
  admin-service:
    build: ./admin-service
    ports:
      - "8001:8000"
    command: sh -c "alembic upgrade head && uvicorn app.main:app --host 0.0.0.0 --port 8000"
    volumes:
      - ./keys/private.pem:/app/keys/private.pem
      - ./keys/public.pem:/app/keys/public.pem
    environment:
      - POSTGRES_SERVER=postgres-admin
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=admin_service
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - AUTH_SERVICE_URL=http://auth-service:8000
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
    depends_on:
      postgres-admin:
        condition: service_healthy
      auth-service:
        condition: service_healthy
      kafka:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "python", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Auth Service
  auth-service:
    build: ./auth-service
    ports:
      - "8002:8000"
    command: sh -c "alembic upgrade head && uvicorn app.main:app --host 0.0.0.0 --port 8000"
    volumes:
      - ./keys/private.pem:/app/keys/private.pem
      - ./keys/public.pem:/app/keys/public.pem
    environment:
      - POSTGRES_SERVER=postgres-auth
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - JWT_SECRET_KEY=supersecretkey
      - RSA_PRIVATE_KEY=${RSA_PRIVATE_KEY:-}
      - RSA_PUBLIC_KEY=${RSA_PUBLIC_KEY:-}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - ENABLE_CACHE=true
      - CACHE_TTL=300
      # Email Configuration for OTP
      - MAIL_USERNAME=${MAIL_USERNAME:-<EMAIL>}
      - MAIL_PASSWORD=${MAIL_PASSWORD:-your-app-password}
      - MAIL_FROM=${MAIL_FROM:-<EMAIL>}
      - MAIL_SERVER=${MAIL_SERVER:-smtp.gmail.com}
      - MAIL_PORT=${MAIL_PORT:-587}
      - MAIL_FROM_NAME=${MAIL_FROM_NAME:-Zionix Auth Service}
      # OTP Configuration
      - OTP_LENGTH=${OTP_LENGTH:-6}
      - OTP_EXPIRE_MINUTES=${OTP_EXPIRE_MINUTES:-5}
      - OTP_MAX_ATTEMPTS=${OTP_MAX_ATTEMPTS:-3}
      - OTP_RATE_LIMIT_MINUTES=${OTP_RATE_LIMIT_MINUTES:-1}
      - OTP_MAX_REQUESTS_PER_WINDOW=${OTP_MAX_REQUESTS_PER_WINDOW:-3}
      # OTP Delivery System Configuration
      - OTP_DELIVERY_ENABLED=${OTP_DELIVERY_ENABLED:-true}
      - OTP_DEFAULT_CHANNELS=${OTP_DEFAULT_CHANNELS:-email,sms}
      - OTP_MAX_DELIVERY_ATTEMPTS=${OTP_MAX_DELIVERY_ATTEMPTS:-3}
      - OTP_DELIVERY_RETRY_DELAY=${OTP_DELIVERY_RETRY_DELAY:-300}
      - OTP_DELIVERY_TIMEOUT=${OTP_DELIVERY_TIMEOUT:-30}
      # SMS Gateway Configuration
      - SMS_GATEWAY_TYPE=${SMS_GATEWAY_TYPE:-gammu}
      - SMS_GATEWAY_CONFIG_FILE=${SMS_GATEWAY_CONFIG_FILE:-/etc/gammurc}
      - SMS_GATEWAY_URL=${SMS_GATEWAY_URL:-}
      - SMS_GATEWAY_API_KEY=${SMS_GATEWAY_API_KEY:-}
      - SMS_ENABLED=${SMS_ENABLED:-true}
      # Celery Configuration
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - CELERY_TASK_SERIALIZER=${CELERY_TASK_SERIALIZER:-json}
      - CELERY_ACCEPT_CONTENT=${CELERY_ACCEPT_CONTENT:-json}
      - CELERY_RESULT_SERIALIZER=${CELERY_RESULT_SERIALIZER:-json}
      - CELERY_TIMEZONE=${CELERY_TIMEZONE:-UTC}
      # Monitoring Configuration
      - PROMETHEUS_ENABLED=${PROMETHEUS_ENABLED:-true}
      - FLOWER_ENABLED=${FLOWER_ENABLED:-true}
      - FLOWER_PORT=${FLOWER_PORT:-5555}
      # Google OAuth Configuration
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID:-your-google-client-id}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET:-your-google-client-secret}
      - GOOGLE_REDIRECT_URI=${GOOGLE_REDIRECT_URI:-http://localhost:8002/api/v1/auth/google/callback}
    depends_on:
      postgres-auth:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s
    restart: unless-stopped
    networks:
      - zcare-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  # Celery Worker for OTP Delivery
  celery-worker:
    build: ./auth-service
    command: celery -A app.core.otp_delivery worker --loglevel=info --queues=otp_delivery,otp_retry
    volumes:
      - ./auth-service:/app
      - /dev:/dev  # For USB GSM modem access
    depends_on:
      redis:
        condition: service_healthy
      postgres-auth:
        condition: service_healthy
    environment:
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=***************************************************/auth_service
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - SMS_GATEWAY_TYPE=gammu
      - SMS_GATEWAY_CONFIG_FILE=/etc/gammurc
      - SMS_ENABLED=true
    privileged: true  # Required for USB device access
    restart: unless-stopped
    networks:
      - zcare-network

  # Celery Beat Scheduler
  celery-beat:
    build: ./auth-service
    command: celery -A app.core.otp_delivery beat --loglevel=info
    volumes:
      - ./auth-service:/app
    depends_on:
      redis:
        condition: service_healthy
      postgres-auth:
        condition: service_healthy
    environment:
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=***************************************************/auth_service
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    restart: unless-stopped
    networks:
      - zcare-network

  # Flower - Celery Monitoring
  flower:
    build: ./auth-service
    command: celery -A app.core.otp_delivery flower --port=5555
    ports:
      - "5555:5555"
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    restart: unless-stopped
    networks:
      - zcare-network

  # Databases
  postgres-admin:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=admin_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-admin-data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d admin_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  postgres-auth:
    image: postgres:13
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Arunnathan
      - POSTGRES_DB=auth_service
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres-auth-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d auth_service"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - zcare-network

  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin123
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "5050:80"
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    depends_on:
      - postgres-admin
      - postgres-auth
    restart: unless-stopped
    networks:
      - zcare-network

  # Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.0.1
    environment:
      - ZOOKEEPER_CLIENT_PORT=2181
      - ZOOKEEPER_TICK_TIME=2000
      - ZOOKEEPER_STANDALONE_ENABLED=true  # Explicit standalone mode
    ports:
      - "2181:2181"
    healthcheck:
      test: ["CMD-SHELL", "zookeeper-ready localhost:2181 10 || exit 0"]  # Increased timeout
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 120s
    restart: unless-stopped
    networks:
      - zcare-network

  kafka:
    image: confluentinc/cp-kafka:7.0.1
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    healthcheck:
      test: ["CMD-SHELL", "kafka-topics --bootstrap-server localhost:9092 --list"]
      interval: 10s
      timeout: 5s
      retries: 10
      start_period: 120s  # Increased from 30s to 120s for slow startup
    restart: unless-stopped
    networks:
      - zcare-network

  # Observability
  prometheus:
    image: prom/prometheus:v2.30.3
    volumes:
      - ./observability/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    networks:
      - zcare-network

  grafana:
    image: grafana/grafana:8.2.2
    volumes:
      - ./observability/grafana/provisioning:/etc/grafana/provisioning
      - ./observability/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - zcare-network

volumes:
  postgres-admin-data:
  postgres-auth-data:
  redis-data:
  pgadmin-data:
  etcd_data:

networks:
  zcare-network:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1450
