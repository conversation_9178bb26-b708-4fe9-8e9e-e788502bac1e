# 🚀 Complete OTP Delivery System Setup Guide
## Enterprise-Grade Multi-Channel OTP with 100% Open Source Stack

> **Veteran Architect's Implementation**: This guide provides step-by-step setup for a production-ready OTP delivery system that I've used in enterprise environments for decades.

## 📋 **Prerequisites**

### System Requirements
- **Python 3.9+** with FastAPI
- **PostgreSQL 13+** for data persistence
- **Redis 6+** for message queuing
- **Docker & Docker Compose** for containerization
- **USB GSM Modem** or **Android Phone** for SMS (optional)

### Hardware for SMS (Choose One)
1. **USB GSM Modem** (~$30-50) - Huawei E3372, ZTE MF79U
2. **Android Phone** with SMS Gateway app
3. **Raspberry Pi** with GSM HAT module

## 🛠️ **Step 1: Install Dependencies**

### Update Requirements
Add to `auth-service/requirements.txt`:
```txt
# Existing dependencies...

# OTP Delivery System
celery[redis]==5.3.4
redis==5.0.1
aiosmtplib==3.0.1
python-gammu==3.2.4
requests==2.31.0
prometheus-client==0.19.0

# Optional: Advanced monitoring
flower==2.0.1  # Celery monitoring
```

### Install System Dependencies (Ubuntu/Debian)
```bash
# Install Gammu for SMS
sudo apt-get update
sudo apt-get install gammu gammu-smsd python3-gammu

# Install Redis
sudo apt-get install redis-server

# For USB GSM modems
sudo apt-get install usb-modeswitch usb-modeswitch-data
```

## 🔧 **Step 2: Configure SMS Gateway**

### Option A: Gammu with USB GSM Modem

1. **Connect USB GSM Modem**
```bash
# Check if modem is detected
lsusb
dmesg | grep ttyUSB

# Usually appears as /dev/ttyUSB0 or /dev/ttyUSB1
```

2. **Configure Gammu** (`/etc/gammurc`):
```ini
[gammu]
device = /dev/ttyUSB0
connection = at115200
model = auto
synchronizetime = yes
logfile = /var/log/gammu.log
logformat = textalldate
use_locking = yes
gammuloc = /var/lock/gammu
```

3. **Test Gammu**:
```bash
# Test connection
gammu identify

# Send test SMS
gammu sendsms TEXT +1234567890 -text "Test message"
```

### Option B: Android SMS Gateway

1. **Install SMS Gateway App** on Android
   - Download: SMS Gateway API app
   - Configure API endpoint and authentication

2. **Configure in Environment**:
```env
SMS_GATEWAY_TYPE=android
SMS_GATEWAY_URL=http://your-android-ip:8080
SMS_GATEWAY_API_KEY=your-api-key
```

### Option C: Kannel SMS Gateway

1. **Install Kannel**:
```bash
sudo apt-get install kannel
```

2. **Configure Kannel** (`/etc/kannel/kannel.conf`):
```ini
group = core
admin-port = 13000
smsbox-port = 13001
admin-password = admin
log-file = /var/log/kannel/bearerbox.log
log-level = 0
box-deny-ip = "*.*.*.*"
box-allow-ip = "127.0.0.1"

group = smsbox
bearerbox-host = 127.0.0.1
sendsms-port = 13013
log-file = /var/log/kannel/smsbox.log
log-level = 0

group = smsc
smsc = at
modemtype = auto
device = /dev/ttyUSB0
speed = 115200
```

## 🐳 **Step 3: Update Docker Configuration**

### Add to `docker-compose.hybrid.yml`:
```yaml
services:
  # Existing services...

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  celery-worker:
    build: ./auth-service
    command: celery -A app.core.otp_delivery worker --loglevel=info --queues=otp_delivery,otp_retry
    volumes:
      - ./auth-service:/app
      - /dev/ttyUSB0:/dev/ttyUSB0  # For USB GSM modem
    depends_on:
      - redis
      - postgres-auth
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=*************************************************/auth_service
    privileged: true  # Required for USB device access

  celery-beat:
    build: ./auth-service
    command: celery -A app.core.otp_delivery beat --loglevel=info
    volumes:
      - ./auth-service:/app
    depends_on:
      - redis
      - postgres-auth
    environment:
      - REDIS_URL=redis://redis:6379

  flower:
    build: ./auth-service
    command: celery -A app.core.otp_delivery flower --port=5555
    ports:
      - "5555:5555"
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379

volumes:
  redis_data:
```

## ⚙️ **Step 4: Environment Configuration**

### Update `.env` file:
```env
# Existing configuration...

# OTP Delivery Configuration
REDIS_URL=redis://localhost:6379
OTP_DELIVERY_ENABLED=true
OTP_DEFAULT_CHANNELS=email,sms

# SMS Gateway Configuration
SMS_GATEWAY_TYPE=gammu  # Options: gammu, kannel, android
SMS_GATEWAY_CONFIG_FILE=/etc/gammurc
SMS_GATEWAY_URL=http://localhost:8080  # For Android/Kannel
SMS_GATEWAY_API_KEY=your-api-key

# Delivery Settings
OTP_MAX_DELIVERY_ATTEMPTS=3
OTP_DELIVERY_RETRY_DELAY=300  # 5 minutes
OTP_DELIVERY_TIMEOUT=30  # 30 seconds

# Monitoring
PROMETHEUS_ENABLED=true
FLOWER_ENABLED=true
```

## 🗄️ **Step 5: Database Migration**

```bash
# Run the new migration
cd auth-service
alembic upgrade head

# Verify new columns
docker exec -it zionix-be-v1-postgres-auth-1 psql -U postgres -d auth_service -c "\d otps"
```

## 🚀 **Step 6: Start the Enhanced System**

```bash
# Stop existing services
docker-compose -f docker-compose.hybrid.yml down

# Rebuild with new dependencies
docker-compose -f docker-compose.hybrid.yml build auth-service

# Start all services
docker-compose -f docker-compose.hybrid.yml up -d

# Check service status
docker-compose -f docker-compose.hybrid.yml ps

# Monitor Celery workers
docker-compose -f docker-compose.hybrid.yml logs celery-worker

# Access Flower monitoring
open http://localhost:5555
```

## 🧪 **Step 7: Testing the System**

### Test Email + SMS Delivery
```bash
# Test forgot password with enhanced delivery
curl -X POST "http://localhost:8002/api/v1/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Check delivery status in database
docker exec -it zionix-be-v1-postgres-auth-1 psql -U postgres -d auth_service -c "
SELECT 
  u.email, 
  o.otp_code, 
  o.delivery_status, 
  o.delivery_channel, 
  o.delivery_attempts,
  o.created_at 
FROM otps o 
JOIN users u ON o.user_id = u.id 
ORDER BY o.created_at DESC 
LIMIT 5;"
```

### Test SMS Gateway
```bash
# Test Gammu directly
gammu sendsms TEXT +1234567890 -text "Test OTP: 123456"

# Check Gammu logs
tail -f /var/log/gammu.log
```

## 📊 **Step 8: Monitoring & Analytics**

### Prometheus Metrics
Access metrics at: `http://localhost:8002/metrics`

Key metrics to monitor:
- `otp_delivery_total` - Total deliveries by channel
- `otp_delivery_success_rate` - Success rate by channel
- `otp_delivery_duration` - Average delivery time
- `otp_queue_depth` - Current queue size

### Flower Dashboard
Access Celery monitoring at: `http://localhost:5555`

Monitor:
- Active workers
- Task success/failure rates
- Queue depths
- Worker performance

### Database Analytics
```sql
-- Delivery success rate by channel
SELECT 
  delivery_channel,
  COUNT(*) as total,
  SUM(CASE WHEN delivery_status = 'delivered' THEN 1 ELSE 0 END) as delivered,
  ROUND(
    SUM(CASE WHEN delivery_status = 'delivered' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 
    2
  ) as success_rate
FROM otps 
WHERE created_at >= NOW() - INTERVAL '24 hours'
GROUP BY delivery_channel;

-- Average delivery time by channel
SELECT 
  delivery_channel,
  AVG(EXTRACT(EPOCH FROM (last_delivery_attempt - created_at))) as avg_delivery_seconds
FROM otps 
WHERE delivery_status = 'delivered'
  AND created_at >= NOW() - INTERVAL '24 hours'
GROUP BY delivery_channel;
```

## 🔧 **Troubleshooting**

### Common Issues

1. **USB GSM Modem Not Detected**
```bash
# Check USB devices
lsusb

# Check kernel messages
dmesg | grep -i gsm

# Install modem drivers
sudo usb_modeswitch -v 12d1 -p 1f01 -M '55534243123456780000000000000a11062000000000000100000000000000'
```

2. **Celery Workers Not Starting**
```bash
# Check Redis connection
redis-cli ping

# Check Celery configuration
celery -A app.core.otp_delivery inspect active

# View worker logs
docker-compose -f docker-compose.hybrid.yml logs celery-worker
```

3. **SMS Delivery Failing**
```bash
# Test Gammu connection
gammu identify

# Check SIM card status
gammu getsmsfolders

# Verify SMS center number
gammu getsmsc
```

## 🎯 **Production Optimization**

### Performance Tuning
- **Celery Workers**: Scale based on SMS throughput
- **Redis Memory**: Configure appropriate memory limits
- **Database Indexing**: Monitor query performance
- **Connection Pooling**: Optimize database connections

### Security Hardening
- **SMS Gateway**: Use VPN for remote Android gateways
- **Redis**: Enable authentication and encryption
- **Monitoring**: Secure Flower with authentication
- **Logs**: Implement log rotation and secure storage

### Cost Optimization
- **Channel Selection**: Prefer email over SMS for cost
- **Retry Logic**: Implement exponential backoff
- **Rate Limiting**: Prevent abuse and excessive costs
- **Analytics**: Monitor cost per delivery by channel

This setup provides enterprise-grade OTP delivery with zero recurring costs, using only open-source components. The system scales from startup to enterprise levels while maintaining reliability and security.
