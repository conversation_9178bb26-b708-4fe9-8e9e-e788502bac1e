# 🔐 Registration with Email Verification Guide

## 📋 Overview

This guide covers the complete registration flow with Google Email OTP verification for the Zionix auth-service. Users must verify their email address before they can log in to their account.

## 🚀 Registration Flow

### Step 1: User Registration
1. **User submits registration form** with email, password, and personal details
2. **Account created with `status=false`** (inactive until email verification)
3. **<PERSON>TP generated** for email verification purpose
4. **Verification email sent** to user's Gmail address
5. **User receives professional email** with 6-digit verification code

### Step 2: Email Verification
1. **User enters OTP code** from email
2. **System verifies OTP** and checks expiration
3. **Account activated** (`status=true`) upon successful verification
4. **User can now log in** to access the application

### Step 3: Login Protection
1. **Login endpoint checks** if email is verified
2. **Unverified accounts** cannot log in (403 Forbidden)
3. **Verified accounts** can log in normally

## 🔧 API Endpoints

### 1. Register User
**POST** `/api/v1/register`

Creates a new user account and sends email verification OTP.

**Request Body (Form Data):**
```
email=<EMAIL>
first_name=John
last_name=Doe
password=SecurePass123!
confirm_password=SecurePass123!
```

**Response:**
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "status": false,
  "action": null,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z"
}
```

### 2. Verify Email
**POST** `/api/v1/verify-email`

Verifies email address using OTP and activates the account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "otp_code": "123456"
}
```

**Response:**
```json
{
  "message": "Email verified successfully! Your account is now active. You can log in.",
  "email": "<EMAIL>",
  "is_verified": true
}
```

### 3. Resend Verification Email
**POST** `/api/v1/resend-verification`

Resends email verification OTP (with rate limiting).

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "message": "If the email exists and is not verified, a new verification code has been sent.",
  "email": "<EMAIL>"
}
```

### 4. Login (Updated)
**POST** `/api/v1/login`

Login now requires email verification.

**Request Body (Form Data):**
```
username=<EMAIL>
password=SecurePass123!
```

**Responses:**
- **Success (200):** Returns access and refresh tokens
- **Email not verified (403):** `"Email not verified. Please check your email and verify your account before logging in."`
- **Invalid credentials (401):** `"Incorrect email or password"`

## 📧 Email Template

The verification email includes:

### Professional Design
- **Zionix branding** with logo and colors
- **Clear call-to-action** with prominent OTP code
- **Security warnings** about code sharing
- **Step-by-step instructions**

### Security Features
- **6-digit OTP code** prominently displayed
- **5-minute expiration** clearly stated
- **Security warnings** about not sharing the code
- **Professional formatting** to prevent phishing confusion

### Content Structure
```
🔐 ZIONIX - EMAIL VERIFICATION REQUIRED

Hello [User Name],

Welcome to Zionix! To complete your registration and activate your account, 
please verify your email address using the following One-Time Password (OTP):

═══════════════════════════════════
VERIFICATION CODE: 123456
═══════════════════════════════════

🛡️ SECURITY INFORMATION:
• This code expires in 5 minutes
• Never share this code with anyone
• We will never ask for your verification code via phone
• If you didn't create this account, please ignore this email

WHAT HAPPENS NEXT:
1. Enter this verification code on the registration page
2. Your account will be activated immediately
3. You'll be able to log in and access all features
```

## 🧪 Testing

### Method 1: Web Interface
Open `test_registration_email_verification.html` in your browser for a complete interactive test.

### Method 2: Manual API Testing

1. **Register User:**
```bash
curl -X POST "http://localhost:8002/api/v1/register" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "email=<EMAIL>&first_name=Test&last_name=User&password=TestPass123!&confirm_password=TestPass123!"
```

2. **Check Email and Verify:**
```bash
curl -X POST "http://localhost:8002/api/v1/verify-email" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","otp_code":"123456"}'
```

3. **Test Login:**
```bash
curl -X POST "http://localhost:8002/api/v1/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=TestPass123!"
```

### Method 3: Frontend Integration
The frontend `auth.js` has been updated with:
- `verifyEmailWithOTP()` function
- `resendVerificationEmail()` function
- Updated registration flow with email verification prompt

## 🛡️ Security Features

### Rate Limiting
- **3 verification requests per minute** per user
- **Prevents spam** and abuse
- **Clear error messages** when rate limit exceeded

### OTP Security
- **Cryptographically secure** random generation
- **5-minute expiration** for security
- **One-time use** - marked as used after verification
- **Purpose-specific** - separate from password reset OTPs

### Account Protection
- **Inactive by default** - accounts cannot be used until verified
- **Email obfuscation** - errors don't reveal if email exists
- **Professional templates** - reduce phishing risk

### Database Security
- **Indexed queries** for performance
- **Proper foreign keys** linking OTPs to users
- **Audit logging** for all verification events

## 🔧 Configuration

All existing OTP configuration applies:

```env
# OTP Configuration
OTP_LENGTH=6                          # Verification code length
OTP_EXPIRE_MINUTES=5                  # Code expiration time
OTP_MAX_ATTEMPTS=3                    # Max verification attempts
OTP_RATE_LIMIT_MINUTES=1              # Rate limit window
OTP_MAX_REQUESTS_PER_WINDOW=3         # Max requests per window

# Email Configuration
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM=<EMAIL>
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_FROM_NAME=Zionix Auth Service
```

## 🎯 Benefits

### User Experience
- **Secure registration** with email verification
- **Professional email templates** build trust
- **Clear error messages** guide users
- **Resend functionality** for convenience

### Security
- **Prevents fake accounts** with unverified emails
- **Reduces spam registrations**
- **Ensures email deliverability** for future communications
- **Audit trail** of all verification attempts

### Business Value
- **Higher quality user base** with verified emails
- **Reduced support tickets** from unverified accounts
- **Better email deliverability** for marketing
- **Compliance** with email verification best practices

## 🚀 Next Steps

1. **Configure Gmail credentials** in environment variables
2. **Test the complete flow** using the test page
3. **Integrate with your frontend** application
4. **Monitor verification rates** and user feedback
5. **Customize email templates** if needed

The registration with email verification system is now **production-ready** with enterprise-grade security and user experience! 🎉
