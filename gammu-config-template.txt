# Gammu Configuration Template for OTP SMS Gateway
# Copy this to /etc/gammurc and modify for your setup

[gammu]
# Device connection - adjust based on your USB GSM modem
device = /dev/ttyUSB0
# Alternative devices to try:
# device = /dev/ttyUSB1
# device = /dev/ttyACM0
# device = /dev/serial/by-id/usb-HUAWEI_HUAWEI_Mobile-if00-port0

# Connection type and speed
connection = at115200
# Alternative connections:
# connection = at19200
# connection = at9600

# Model detection
model = auto

# Synchronize time with computer
synchronizetime = yes

# Logging
logfile = /var/log/gammu.log
logformat = textalldate

# Locking to prevent conflicts
use_locking = yes
gammuloc = /var/lock/gammu

# PIN code (if S<PERSON> requires it)
# pin = 1234

# SMS settings
# smsbackend = files
# smssend = yes

# Network settings (optional)
# networkinfo = yes

# Example configurations for different modems:

# Huawei E3372 USB Modem
# [gammu]
# device = /dev/ttyUSB0
# connection = at115200
# model = auto

# ZTE MF79U USB Modem  
# [gammu]
# device = /dev/ttyUSB1
# connection = at115200
# model = auto

# Raspberry Pi with GSM HAT
# [gammu]
# device = /dev/serial0
# connection = at115200
# model = auto

# Android phone via USB (requires USB debugging)
# [gammu]
# device = /dev/ttyACM0
# connection = at115200
# model = auto

# Setup Instructions:
# 1. Connect your USB GSM modem
# 2. Check device with: lsusb and dmesg | grep tty
# 3. Copy this file to /etc/gammurc
# 4. Modify device path and connection settings
# 5. Test with: gammu identify
# 6. Send test SMS: gammu sendsms TEXT +1234567890 -text "Test"

# Troubleshooting:
# - If device not found: check USB connection and drivers
# - If permission denied: add user to dialout group
# - If SIM not detected: check PIN code and SIM card
# - If SMS fails: check network registration and credit
