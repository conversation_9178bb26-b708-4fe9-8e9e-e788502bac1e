# 🔧 Email OTP Troubleshooting Guide

## 🚨 Issue: OTP Email Not Appearing in Gmail

Based on your screenshot, the OTP email is not being delivered to Gmail. Here are the most common causes and solutions:

## 🔍 **Immediate Checks**

### 1. Check Spam/Junk Folder
- **Gmail Spam Folder**: Check your spam folder first
- **Promotions Tab**: Check the "Promotions" tab in Gmail
- **All Mail**: Search in "All Mail" for "Zionix" or "OTP"

### 2. Check Email Configuration
The current configuration shows hardcoded credentials that need to be updated:

```python
MAIL_USERNAME=os.getenv("MAIL_USERNAME", "Zionix")  # ❌ Not a valid email
MAIL_PASSWORD=os.getenv("MAIL_PASSWORD", "<PERSON><PERSON>@1234")  # ❌ Not an app password
MAIL_FROM=os.getenv("MAIL_FROM", "<EMAIL>")  # ❌ May not be authorized
```

## 🛠️ **Solutions**

### Solution 1: Fix Gmail SMTP Configuration

1. **Set up Gmail App Password:**
   ```bash
   # Go to Google Account settings
   # Enable 2-Factor Authentication
   # Generate App Password for "Mail"
   ```

2. **Update Environment Variables:**
   ```env
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your-16-char-app-password
   MAIL_FROM=<EMAIL>
   MAIL_SERVER=smtp.gmail.com
   MAIL_PORT=587
   ```

### Solution 2: Use Gmail OAuth (Recommended)

Since we implemented Gmail OAuth, use that instead:

1. **Test Gmail OAuth Flow:**
   - Open: `test_gmail_oauth_password_reset.html`
   - Login with your account
   - Authorize Gmail access
   - Send OTP via Gmail API

### Solution 3: Quick Test with Working Email

Let me create a test script to verify email functionality:

## 📧 **Email Delivery Test Script**

```python
# Test script to verify email configuration
import asyncio
from app.core.email import send_otp_email

async def test_email():
    result = await send_otp_email(
        email="<EMAIL>",
        otp_code="123456",
        user_name="Test User"
    )
    print(f"Email sent: {result}")

# Run test
asyncio.run(test_email())
```

## 🔧 **Debug Steps**

### Step 1: Check Service Logs
```bash
# Check auth-service logs for email errors
docker-compose -f docker-compose.hybrid.yml logs auth-service | grep -i mail

# Check for SMTP errors
docker-compose -f docker-compose.hybrid.yml logs auth-service | grep -i smtp
```

### Step 2: Test SMTP Connection
```bash
# Test SMTP connection manually
telnet smtp.gmail.com 587
```

### Step 3: Verify Environment Variables
```bash
# Check if environment variables are loaded
docker-compose -f docker-compose.hybrid.yml exec auth-service env | grep MAIL
```

## 🎯 **Recommended Fix**

Based on the current setup, here's the immediate fix:

### Option A: Use Gmail OAuth (Best)
1. Open `test_gmail_oauth_password_reset.html`
2. Complete OAuth authorization
3. Use Gmail API to send emails

### Option B: Fix SMTP Configuration
1. Set up proper Gmail App Password
2. Update environment variables
3. Restart services

## 🚀 **Quick Fix Commands**

```bash
# 1. Stop services
docker-compose -f docker-compose.hybrid.yml down

# 2. Update .env file with correct Gmail credentials
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password

# 3. Restart services
docker-compose -f docker-compose.hybrid.yml up -d

# 4. Test email sending
curl -X POST "http://localhost:8002/api/v1/forgot-password" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

## 📱 **Alternative Testing Methods**

### Method 1: Check Database for OTP
```bash
# Check if OTP was created in database
docker-compose -f docker-compose.hybrid.yml exec postgres-auth psql -U postgres -d auth_service -c "SELECT email, otp_code, created_at, expires_at FROM otps ORDER BY created_at DESC LIMIT 5;"
```

### Method 2: Use Different Email Provider
```env
# Try with a different email service temporarily
MAIL_SERVER=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your-mailtrap-username
MAIL_PASSWORD=your-mailtrap-password
```

### Method 3: Enable Debug Logging
```python
# Add to email.py for debugging
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🔍 **Common Issues & Solutions**

| Issue | Cause | Solution |
|-------|-------|----------|
| Email not sent | Invalid SMTP credentials | Set up Gmail App Password |
| Email in spam | Missing SPF/DKIM | Use Gmail OAuth instead |
| Connection timeout | Firewall/network issue | Check Docker network settings |
| Authentication failed | Wrong password format | Use 16-character app password |
| Rate limited | Too many requests | Wait and implement rate limiting |

## ✅ **Verification Steps**

1. **Check OTP in Database:**
   - Verify OTP was created
   - Check expiration time
   - Confirm user association

2. **Check Email Logs:**
   - Look for SMTP errors
   - Verify connection attempts
   - Check authentication status

3. **Test Email Delivery:**
   - Use Gmail OAuth method
   - Try different recipient email
   - Check spam folders

## 🎯 **Next Steps**

1. **Immediate:** Check spam folder and promotions tab
2. **Short-term:** Set up proper Gmail App Password
3. **Long-term:** Use Gmail OAuth for better deliverability

The Gmail OAuth method is recommended as it provides better deliverability and doesn't require managing SMTP credentials.
