"""
OTP Delivery Analytics and Monitoring API
Provides insights into delivery performance, costs, and user preferences
"""

from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from pydantic import BaseModel

from app.db.session import get_db
from app.models.otp import OTP
from app.models.user import User
from app.core.auth import get_current_user

router = APIRouter(prefix="/analytics", tags=["OTP Analytics"])

class DeliveryStats(BaseModel):
    """Delivery statistics by channel"""
    channel: str
    total_sent: int
    delivered: int
    failed: int
    success_rate: float
    avg_delivery_time_seconds: Optional[float]
    total_cost: float

class DeliveryTrends(BaseModel):
    """Delivery trends over time"""
    date: str
    email_sent: int
    email_delivered: int
    sms_sent: int
    sms_delivered: int
    total_cost: float

class UserPreferences(BaseModel):
    """User delivery preferences analysis"""
    preferred_channel: str
    user_count: int
    success_rate: float

class SystemHealth(BaseModel):
    """Overall system health metrics"""
    total_otps_24h: int
    delivery_success_rate: float
    avg_delivery_time: float
    failed_deliveries: int
    queue_depth: int
    cost_per_otp: float

class ChannelPerformance(BaseModel):
    """Detailed channel performance metrics"""
    channel: str
    availability: float
    avg_response_time: float
    error_rate: float
    cost_efficiency: float  # Deliveries per dollar

@router.get("/delivery-stats", response_model=List[DeliveryStats])
async def get_delivery_stats(
    days: int = Query(7, ge=1, le=90, description="Number of days to analyze"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get delivery statistics by channel for the specified period"""
    
    start_date = datetime.now(timezone.utc) - timedelta(days=days)
    
    # Query delivery stats by channel
    stats_query = db.query(
        OTP.delivery_channel,
        func.count(OTP.id).label('total_sent'),
        func.sum(func.case([(OTP.delivery_status == 'delivered', 1)], else_=0)).label('delivered'),
        func.sum(func.case([(OTP.delivery_status == 'failed', 1)], else_=0)).label('failed'),
        func.avg(
            func.extract('epoch', OTP.last_delivery_attempt - OTP.created_at)
        ).label('avg_delivery_time'),
        func.sum(OTP.delivery_cost).label('total_cost')
    ).filter(
        OTP.created_at >= start_date,
        OTP.delivery_channel.isnot(None)
    ).group_by(OTP.delivery_channel)
    
    results = []
    for row in stats_query.all():
        delivered = row.delivered or 0
        total_sent = row.total_sent or 0
        success_rate = (delivered / total_sent * 100) if total_sent > 0 else 0
        
        results.append(DeliveryStats(
            channel=row.delivery_channel or 'unknown',
            total_sent=total_sent,
            delivered=delivered,
            failed=row.failed or 0,
            success_rate=round(success_rate, 2),
            avg_delivery_time_seconds=row.avg_delivery_time,
            total_cost=row.total_cost or 0.0
        ))
    
    return results

@router.get("/delivery-trends", response_model=List[DeliveryTrends])
async def get_delivery_trends(
    days: int = Query(30, ge=7, le=90, description="Number of days to analyze"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get daily delivery trends"""
    
    start_date = datetime.now(timezone.utc) - timedelta(days=days)
    
    # Query daily trends
    trends_query = db.query(
        func.date(OTP.created_at).label('date'),
        func.sum(func.case([(OTP.delivery_channel == 'email', 1)], else_=0)).label('email_sent'),
        func.sum(func.case([
            (and_(OTP.delivery_channel == 'email', OTP.delivery_status == 'delivered'), 1)
        ], else_=0)).label('email_delivered'),
        func.sum(func.case([(OTP.delivery_channel == 'sms', 1)], else_=0)).label('sms_sent'),
        func.sum(func.case([
            (and_(OTP.delivery_channel == 'sms', OTP.delivery_status == 'delivered'), 1)
        ], else_=0)).label('sms_delivered'),
        func.sum(OTP.delivery_cost).label('total_cost')
    ).filter(
        OTP.created_at >= start_date
    ).group_by(
        func.date(OTP.created_at)
    ).order_by(
        func.date(OTP.created_at)
    )
    
    results = []
    for row in trends_query.all():
        results.append(DeliveryTrends(
            date=row.date.isoformat(),
            email_sent=row.email_sent or 0,
            email_delivered=row.email_delivered or 0,
            sms_sent=row.sms_sent or 0,
            sms_delivered=row.sms_delivered or 0,
            total_cost=row.total_cost or 0.0
        ))
    
    return results

@router.get("/user-preferences", response_model=List[UserPreferences])
async def get_user_preferences(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Analyze user delivery channel preferences"""
    
    # Get user preferences based on successful deliveries
    preferences_query = db.query(
        OTP.delivery_channel,
        func.count(func.distinct(OTP.user_id)).label('user_count'),
        func.avg(func.case([(OTP.delivery_status == 'delivered', 100.0)], else_=0)).label('success_rate')
    ).filter(
        OTP.delivery_channel.isnot(None),
        OTP.created_at >= datetime.now(timezone.utc) - timedelta(days=30)
    ).group_by(OTP.delivery_channel)
    
    results = []
    for row in preferences_query.all():
        results.append(UserPreferences(
            preferred_channel=row.delivery_channel,
            user_count=row.user_count or 0,
            success_rate=round(row.success_rate or 0, 2)
        ))
    
    return results

@router.get("/system-health", response_model=SystemHealth)
async def get_system_health(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get overall system health metrics"""
    
    # 24-hour window
    start_time = datetime.now(timezone.utc) - timedelta(hours=24)
    
    # Total OTPs in last 24 hours
    total_otps = db.query(func.count(OTP.id)).filter(
        OTP.created_at >= start_time
    ).scalar() or 0
    
    # Delivered OTPs
    delivered_otps = db.query(func.count(OTP.id)).filter(
        OTP.created_at >= start_time,
        OTP.delivery_status == 'delivered'
    ).scalar() or 0
    
    # Failed deliveries
    failed_otps = db.query(func.count(OTP.id)).filter(
        OTP.created_at >= start_time,
        OTP.delivery_status == 'failed'
    ).scalar() or 0
    
    # Pending/queued (queue depth)
    queue_depth = db.query(func.count(OTP.id)).filter(
        OTP.delivery_status.in_(['pending', 'queued', 'sending'])
    ).scalar() or 0
    
    # Average delivery time
    avg_delivery_time = db.query(
        func.avg(func.extract('epoch', OTP.last_delivery_attempt - OTP.created_at))
    ).filter(
        OTP.created_at >= start_time,
        OTP.delivery_status == 'delivered',
        OTP.last_delivery_attempt.isnot(None)
    ).scalar() or 0
    
    # Total cost
    total_cost = db.query(func.sum(OTP.delivery_cost)).filter(
        OTP.created_at >= start_time
    ).scalar() or 0.0
    
    # Calculate metrics
    success_rate = (delivered_otps / total_otps * 100) if total_otps > 0 else 0
    cost_per_otp = (total_cost / total_otps) if total_otps > 0 else 0
    
    return SystemHealth(
        total_otps_24h=total_otps,
        delivery_success_rate=round(success_rate, 2),
        avg_delivery_time=round(avg_delivery_time, 2),
        failed_deliveries=failed_otps,
        queue_depth=queue_depth,
        cost_per_otp=round(cost_per_otp, 4)
    )

@router.get("/channel-performance", response_model=List[ChannelPerformance])
async def get_channel_performance(
    hours: int = Query(24, ge=1, le=168, description="Hours to analyze"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get detailed channel performance metrics"""
    
    start_time = datetime.now(timezone.utc) - timedelta(hours=hours)
    
    # Query channel performance
    performance_query = db.query(
        OTP.delivery_channel,
        func.count(OTP.id).label('total_attempts'),
        func.sum(func.case([(OTP.delivery_status == 'delivered', 1)], else_=0)).label('successful'),
        func.sum(func.case([(OTP.delivery_status == 'failed', 1)], else_=0)).label('failed'),
        func.avg(
            func.extract('epoch', OTP.last_delivery_attempt - OTP.created_at)
        ).label('avg_response_time'),
        func.sum(OTP.delivery_cost).label('total_cost')
    ).filter(
        OTP.created_at >= start_time,
        OTP.delivery_channel.isnot(None)
    ).group_by(OTP.delivery_channel)
    
    results = []
    for row in performance_query.all():
        total_attempts = row.total_attempts or 0
        successful = row.successful or 0
        failed = row.failed or 0
        total_cost = row.total_cost or 0.0
        
        availability = (successful / total_attempts * 100) if total_attempts > 0 else 0
        error_rate = (failed / total_attempts * 100) if total_attempts > 0 else 0
        cost_efficiency = (successful / total_cost) if total_cost > 0 else 0
        
        results.append(ChannelPerformance(
            channel=row.delivery_channel,
            availability=round(availability, 2),
            avg_response_time=round(row.avg_response_time or 0, 2),
            error_rate=round(error_rate, 2),
            cost_efficiency=round(cost_efficiency, 2)
        ))
    
    return results

@router.get("/failed-deliveries")
async def get_failed_deliveries(
    limit: int = Query(50, ge=1, le=200, description="Number of records to return"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get recent failed deliveries for troubleshooting"""
    
    failed_deliveries = db.query(
        OTP.id,
        OTP.otp_code,
        OTP.delivery_channel,
        OTP.delivery_error,
        OTP.delivery_attempts,
        OTP.created_at,
        OTP.last_delivery_attempt,
        User.email
    ).join(User, OTP.user_id == User.id).filter(
        OTP.delivery_status == 'failed'
    ).order_by(
        OTP.created_at.desc()
    ).limit(limit).all()
    
    results = []
    for delivery in failed_deliveries:
        results.append({
            'otp_id': delivery.id,
            'user_email': delivery.email,
            'channel': delivery.delivery_channel,
            'error': delivery.delivery_error,
            'attempts': delivery.delivery_attempts,
            'created_at': delivery.created_at.isoformat(),
            'last_attempt': delivery.last_delivery_attempt.isoformat() if delivery.last_delivery_attempt else None
        })
    
    return results

@router.post("/retry-failed/{otp_id}")
async def retry_failed_delivery(
    otp_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Manually retry a failed OTP delivery"""
    
    otp = db.query(OTP).filter(OTP.id == otp_id).first()
    if not otp:
        raise HTTPException(status_code=404, detail="OTP not found")
    
    if otp.delivery_status != 'failed':
        raise HTTPException(status_code=400, detail="OTP is not in failed state")
    
    # Reset delivery status for retry
    otp.delivery_status = 'pending'
    otp.delivery_error = None
    db.commit()
    
    # Queue for retry (would integrate with your delivery service)
    # await otp_delivery_service.queue_delivery(...)
    
    return {"message": f"OTP {otp_id} queued for retry", "otp_id": otp_id}

@router.get("/export-analytics")
async def export_analytics(
    days: int = Query(30, ge=1, le=365, description="Days to export"),
    format: str = Query("json", regex="^(json|csv)$", description="Export format"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Export analytics data for external analysis"""
    
    start_date = datetime.now(timezone.utc) - timedelta(days=days)
    
    # Get comprehensive analytics data
    analytics_data = db.query(
        OTP.id,
        OTP.purpose,
        OTP.delivery_channel,
        OTP.delivery_status,
        OTP.delivery_attempts,
        OTP.delivery_cost,
        OTP.created_at,
        OTP.last_delivery_attempt,
        User.email.label('user_email')
    ).join(User, OTP.user_id == User.id).filter(
        OTP.created_at >= start_date
    ).order_by(OTP.created_at.desc()).all()
    
    if format == "csv":
        # Return CSV format
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'otp_id', 'user_email', 'purpose', 'delivery_channel', 
            'delivery_status', 'delivery_attempts', 'delivery_cost',
            'created_at', 'last_delivery_attempt'
        ])
        
        # Write data
        for row in analytics_data:
            writer.writerow([
                row.id, row.user_email, row.purpose, row.delivery_channel,
                row.delivery_status, row.delivery_attempts, row.delivery_cost,
                row.created_at.isoformat(), 
                row.last_delivery_attempt.isoformat() if row.last_delivery_attempt else None
            ])
        
        return {"data": output.getvalue(), "format": "csv"}
    
    else:
        # Return JSON format
        results = []
        for row in analytics_data:
            results.append({
                'otp_id': row.id,
                'user_email': row.user_email,
                'purpose': row.purpose,
                'delivery_channel': row.delivery_channel,
                'delivery_status': row.delivery_status,
                'delivery_attempts': row.delivery_attempts,
                'delivery_cost': row.delivery_cost,
                'created_at': row.created_at.isoformat(),
                'last_delivery_attempt': row.last_delivery_attempt.isoformat() if row.last_delivery_attempt else None
            })
        
        return {"data": results, "format": "json", "total_records": len(results)}
