"""
Open-Source SMS Gateway Integration
Supports Gammu, Kannel, and other free SMS solutions
"""

import asyncio
import logging
import subprocess
import tempfile
import os
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json
import requests
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class SMSStatus(str, Enum):
    PENDING = "pending"
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    UNKNOWN = "unknown"

@dataclass
class SMSResult:
    success: bool
    message_id: Optional[str] = None
    status: SMSStatus = SMSStatus.PENDING
    error_message: Optional[str] = None
    cost: float = 0.0  # Track cost per SMS

class BaseSMSGateway(ABC):
    """Abstract base class for SMS gateways"""
    
    @abstractmethod
    async def send_sms(self, phone_number: str, message: str) -> SMSResult:
        """Send SMS message"""
        pass
    
    @abstractmethod
    async def check_status(self, message_id: str) -> SMSStatus:
        """Check SMS delivery status"""
        pass
    
    @abstractmethod
    async def get_balance(self) -> float:
        """Get account balance (if applicable)"""
        pass

class GammuSMSGateway(BaseSMSGateway):
    """
    Gammu SMS Gateway - Free, self-hosted SMS solution
    Requires: USB GSM modem or Android phone with SMS gateway app
    """
    
    def __init__(self, config_file: str = "/etc/gammurc"):
        self.config_file = config_file
        self.enabled = self._check_gammu_installation()
    
    def _check_gammu_installation(self) -> bool:
        """Check if Gammu is installed and configured"""
        try:
            result = subprocess.run(
                ["gammu", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            logger.warning("Gammu not found or not responding")
            return False
    
    async def send_sms(self, phone_number: str, message: str) -> SMSResult:
        """Send SMS using Gammu command line"""
        if not self.enabled:
            return SMSResult(
                success=False,
                error_message="Gammu not available"
            )
        
        try:
            # Clean phone number (remove non-digits except +)
            clean_phone = ''.join(c for c in phone_number if c.isdigit() or c == '+')
            
            if not clean_phone:
                return SMSResult(
                    success=False,
                    error_message="Invalid phone number"
                )
            
            # Create temporary file for SMS content
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
                f.write(message)
                temp_file = f.name
            
            try:
                # Send SMS using Gammu
                cmd = [
                    "gammu",
                    "-c", self.config_file,
                    "sendsms",
                    "TEXT",
                    clean_phone,
                    "-text", message
                ]
                
                result = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await result.communicate()
                
                if result.returncode == 0:
                    # Parse Gammu output for message ID
                    output = stdout.decode('utf-8')
                    message_id = self._extract_message_id(output)
                    
                    logger.info(f"SMS sent successfully to {clean_phone}")
                    return SMSResult(
                        success=True,
                        message_id=message_id,
                        status=SMSStatus.SENT
                    )
                else:
                    error_msg = stderr.decode('utf-8')
                    logger.error(f"Gammu SMS failed: {error_msg}")
                    return SMSResult(
                        success=False,
                        error_message=error_msg
                    )
                    
            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file)
                except OSError:
                    pass
                    
        except Exception as e:
            logger.error(f"SMS sending error: {str(e)}")
            return SMSResult(
                success=False,
                error_message=str(e)
            )
    
    def _extract_message_id(self, gammu_output: str) -> Optional[str]:
        """Extract message ID from Gammu output"""
        # Gammu typically outputs something like "Message sent, ID: 123"
        lines = gammu_output.split('\n')
        for line in lines:
            if 'ID:' in line or 'Reference:' in line:
                parts = line.split(':')
                if len(parts) > 1:
                    return parts[1].strip()
        return None
    
    async def check_status(self, message_id: str) -> SMSStatus:
        """Check SMS status using Gammu"""
        if not self.enabled or not message_id:
            return SMSStatus.UNKNOWN
        
        try:
            cmd = ["gammu", "-c", self.config_file, "getsms", message_id]
            result = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                output = stdout.decode('utf-8').lower()
                if 'delivered' in output:
                    return SMSStatus.DELIVERED
                elif 'sent' in output:
                    return SMSStatus.SENT
                else:
                    return SMSStatus.PENDING
            else:
                return SMSStatus.UNKNOWN
                
        except Exception as e:
            logger.error(f"Status check error: {str(e)}")
            return SMSStatus.UNKNOWN
    
    async def get_balance(self) -> float:
        """Get SIM card balance (if supported by carrier)"""
        try:
            # This depends on your carrier's USSD codes
            # Common balance check codes: *100#, *101#, *121#
            cmd = ["gammu", "-c", self.config_file, "getussd", "*100#"]
            result = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                # Parse balance from USSD response
                # This is carrier-specific and would need customization
                return 0.0  # Placeholder
            else:
                return 0.0
                
        except Exception:
            return 0.0

class KannelSMSGateway(BaseSMSGateway):
    """
    Kannel SMS Gateway - Open source SMS gateway
    Supports multiple SMS connections and protocols
    """
    
    def __init__(self, base_url: str, username: str, password: str):
        self.base_url = base_url.rstrip('/')
        self.username = username
        self.password = password
        self.session = requests.Session()
    
    async def send_sms(self, phone_number: str, message: str) -> SMSResult:
        """Send SMS via Kannel HTTP interface"""
        try:
            url = f"{self.base_url}/cgi-bin/sendsms"
            params = {
                'username': self.username,
                'password': self.password,
                'to': phone_number,
                'text': message,
                'charset': 'UTF-8'
            }
            
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.session.get(url, params=params, timeout=30)
            )
            
            if response.status_code == 200:
                # Kannel returns different responses based on configuration
                content = response.text.strip()
                
                if content.startswith('0:'):  # Success
                    message_id = content.split(':')[1] if ':' in content else None
                    return SMSResult(
                        success=True,
                        message_id=message_id,
                        status=SMSStatus.SENT
                    )
                else:  # Error
                    return SMSResult(
                        success=False,
                        error_message=content
                    )
            else:
                return SMSResult(
                    success=False,
                    error_message=f"HTTP {response.status_code}: {response.text}"
                )
                
        except Exception as e:
            logger.error(f"Kannel SMS error: {str(e)}")
            return SMSResult(
                success=False,
                error_message=str(e)
            )
    
    async def check_status(self, message_id: str) -> SMSStatus:
        """Check SMS status via Kannel"""
        try:
            url = f"{self.base_url}/cgi-bin/dlr-query"
            params = {
                'username': self.username,
                'password': self.password,
                'message_id': message_id
            }
            
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.session.get(url, params=params, timeout=10)
            )
            
            if response.status_code == 200:
                content = response.text.strip().lower()
                if 'delivered' in content:
                    return SMSStatus.DELIVERED
                elif 'sent' in content:
                    return SMSStatus.SENT
                elif 'failed' in content:
                    return SMSStatus.FAILED
                else:
                    return SMSStatus.PENDING
            else:
                return SMSStatus.UNKNOWN
                
        except Exception as e:
            logger.error(f"Kannel status check error: {str(e)}")
            return SMSStatus.UNKNOWN
    
    async def get_balance(self) -> float:
        """Get account balance from Kannel"""
        # Kannel doesn't typically provide balance info
        # This would depend on your SMS provider integration
        return 0.0

class AndroidSMSGateway(BaseSMSGateway):
    """
    Android SMS Gateway - Use Android phone as SMS gateway
    Requires SMS Gateway app on Android device
    """
    
    def __init__(self, gateway_url: str, api_key: str):
        self.gateway_url = gateway_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({'Authorization': f'Bearer {api_key}'})
    
    async def send_sms(self, phone_number: str, message: str) -> SMSResult:
        """Send SMS via Android SMS Gateway app"""
        try:
            url = f"{self.gateway_url}/api/send"
            payload = {
                'phone': phone_number,
                'message': message,
                'priority': 1
            }
            
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.session.post(url, json=payload, timeout=30)
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    return SMSResult(
                        success=True,
                        message_id=data.get('id'),
                        status=SMSStatus.SENT
                    )
                else:
                    return SMSResult(
                        success=False,
                        error_message=data.get('error', 'Unknown error')
                    )
            else:
                return SMSResult(
                    success=False,
                    error_message=f"HTTP {response.status_code}"
                )
                
        except Exception as e:
            logger.error(f"Android SMS Gateway error: {str(e)}")
            return SMSResult(
                success=False,
                error_message=str(e)
            )
    
    async def check_status(self, message_id: str) -> SMSStatus:
        """Check SMS status via Android gateway"""
        try:
            url = f"{self.gateway_url}/api/status/{message_id}"
            
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.session.get(url, timeout=10)
            )
            
            if response.status_code == 200:
                data = response.json()
                status = data.get('status', '').lower()
                
                status_map = {
                    'delivered': SMSStatus.DELIVERED,
                    'sent': SMSStatus.SENT,
                    'failed': SMSStatus.FAILED,
                    'pending': SMSStatus.PENDING
                }
                
                return status_map.get(status, SMSStatus.UNKNOWN)
            else:
                return SMSStatus.UNKNOWN
                
        except Exception as e:
            logger.error(f"Android gateway status check error: {str(e)}")
            return SMSStatus.UNKNOWN
    
    async def get_balance(self) -> float:
        """Get device balance info"""
        try:
            url = f"{self.gateway_url}/api/balance"
            
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.session.get(url, timeout=10)
            )
            
            if response.status_code == 200:
                data = response.json()
                return float(data.get('balance', 0.0))
            else:
                return 0.0
                
        except Exception:
            return 0.0

class SMSGatewayManager:
    """Manages multiple SMS gateways with failover"""
    
    def __init__(self):
        self.gateways: List[BaseSMSGateway] = []
        self.primary_gateway: Optional[BaseSMSGateway] = None
        self._load_gateways()
    
    def _load_gateways(self):
        """Load and configure available SMS gateways"""
        # Try to initialize Gammu gateway
        try:
            gammu_gateway = GammuSMSGateway()
            if gammu_gateway.enabled:
                self.gateways.append(gammu_gateway)
                if not self.primary_gateway:
                    self.primary_gateway = gammu_gateway
                logger.info("Gammu SMS gateway initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Gammu gateway: {e}")
        
        # Add other gateways based on configuration
        # This would be configured via environment variables
        
    def add_gateway(self, gateway: BaseSMSGateway, is_primary: bool = False):
        """Add a new SMS gateway"""
        self.gateways.append(gateway)
        if is_primary or not self.primary_gateway:
            self.primary_gateway = gateway
    
    async def send_sms(self, phone_number: str, message: str) -> SMSResult:
        """Send SMS with automatic failover"""
        if not self.gateways:
            return SMSResult(
                success=False,
                error_message="No SMS gateways configured"
            )
        
        # Try primary gateway first
        if self.primary_gateway:
            result = await self.primary_gateway.send_sms(phone_number, message)
            if result.success:
                return result
        
        # Try other gateways
        for gateway in self.gateways:
            if gateway == self.primary_gateway:
                continue
            
            try:
                result = await gateway.send_sms(phone_number, message)
                if result.success:
                    return result
            except Exception as e:
                logger.error(f"Gateway {type(gateway).__name__} failed: {e}")
                continue
        
        return SMSResult(
            success=False,
            error_message="All SMS gateways failed"
        )
    
    async def check_status(self, message_id: str, gateway_type: Optional[str] = None) -> SMSStatus:
        """Check SMS status across gateways"""
        for gateway in self.gateways:
            if gateway_type and type(gateway).__name__ != gateway_type:
                continue
            
            try:
                status = await gateway.check_status(message_id)
                if status != SMSStatus.UNKNOWN:
                    return status
            except Exception as e:
                logger.error(f"Status check failed for {type(gateway).__name__}: {e}")
                continue
        
        return SMSStatus.UNKNOWN

# Global SMS gateway manager instance
sms_gateway_manager = SMSGatewayManager()
