"""
OAuth 2.0 authentication routes for Google integration
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.core.gmail_oauth import gmail_oauth_manager
from app.crud.oauth_token import (
    create_or_update_oauth_token,
    get_oauth_token,
    get_valid_oauth_token,
    refresh_oauth_token,
    revoke_oauth_token,
    is_token_expired
)
from app.crud.user import get_user_by_email
from app.api.dependencies import get_current_user
from app.models.user import User
from app.schemas.oauth import (
    GoogleAuthRequest,
    GoogleAuthResponse,
    GoogleCallbackRequest,
    GoogleCallbackResponse,
    OAuthTokenResponse,
    RefreshTokenRequest,
    RefreshTokenResponse,
    RevokeTokenRequest,
    RevokeTokenResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth/google", tags=["oauth"])


@router.post("/authorize", response_model=GoogleAuthResponse)
async def google_authorize(
    request: GoogleAuthRequest,
    current_user: User = Depends(get_current_user)
):
    """Initiate Google OAuth 2.0 authorization flow"""
    try:
        # Generate state parameter with user ID for security
        state = f"user_{current_user.id}_{request.state or 'default'}"
        
        # Get authorization URL
        authorization_url = gmail_oauth_manager.get_authorization_url(state=state)
        
        logger.info(f"Generated Google OAuth authorization URL for user {current_user.id}")
        
        return GoogleAuthResponse(
            authorization_url=authorization_url,
            state=state
        )
        
    except Exception as e:
        logger.error(f"Error in google_authorize: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate authorization URL"
        )


@router.post("/callback", response_model=GoogleCallbackResponse)
async def google_callback(
    request: GoogleCallbackRequest,
    db: Session = Depends(get_db)
):
    """Handle Google OAuth 2.0 callback"""
    try:
        # Extract user ID from state parameter
        if not request.state or not request.state.startswith("user_"):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid state parameter"
            )
        
        try:
            user_id = int(request.state.split("_")[1])
        except (IndexError, ValueError):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid state parameter format"
            )
        
        # Exchange authorization code for tokens
        token_data = gmail_oauth_manager.exchange_code_for_tokens(
            authorization_code=request.code,
            state=request.state
        )
        
        # Store tokens in database
        oauth_token = create_or_update_oauth_token(
            db=db,
            user_id=user_id,
            provider="google",
            token_data=token_data
        )
        
        logger.info(f"Successfully stored Google OAuth tokens for user {user_id}")
        
        return GoogleCallbackResponse(
            access_token=token_data["access_token"],
            refresh_token=token_data.get("refresh_token"),
            expires_in=token_data.get("expires_in"),
            scope=token_data.get("scope"),
            user_info=token_data.get("user_info", {})
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in google_callback: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process OAuth callback"
        )


@router.get("/token", response_model=OAuthTokenResponse)
async def get_google_token(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current Google OAuth token for the user"""
    try:
        oauth_token = get_oauth_token(db, current_user.id, "google")
        
        if not oauth_token:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No Google OAuth token found. Please authorize first."
            )
        
        return OAuthTokenResponse.model_validate(oauth_token)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting Google token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve OAuth token"
        )


@router.post("/refresh", response_model=RefreshTokenResponse)
async def refresh_google_token(
    request: RefreshTokenRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Refresh Google OAuth access token"""
    try:
        oauth_token = get_oauth_token(db, current_user.id, "google")
        
        if not oauth_token or not oauth_token.refresh_token:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No refresh token found. Please re-authorize."
            )
        
        # Refresh the token
        new_token_data = gmail_oauth_manager.refresh_access_token(
            oauth_token.refresh_token
        )
        
        # Update token in database
        updated_token = refresh_oauth_token(
            db=db,
            user_id=current_user.id,
            provider="google",
            new_token_data=new_token_data
        )
        
        if not updated_token:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update refreshed token"
            )
        
        logger.info(f"Refreshed Google OAuth token for user {current_user.id}")
        
        return RefreshTokenResponse(
            access_token=new_token_data["access_token"],
            expires_in=new_token_data.get("expires_in"),
            message="Token refreshed successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error refreshing Google token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh OAuth token"
        )


@router.post("/revoke", response_model=RevokeTokenResponse)
async def revoke_google_token(
    request: RevokeTokenRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Revoke Google OAuth token"""
    try:
        success = revoke_oauth_token(db, current_user.id, "google")
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No Google OAuth token found to revoke"
            )
        
        logger.info(f"Revoked Google OAuth token for user {current_user.id}")
        
        return RevokeTokenResponse(
            message="Google OAuth token revoked successfully",
            provider="google",
            revoked=True
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error revoking Google token: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke OAuth token"
        )


@router.get("/status")
async def google_oauth_status(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Check Google OAuth authorization status"""
    try:
        oauth_token = get_oauth_token(db, current_user.id, "google")
        
        if not oauth_token:
            return {
                "authorized": False,
                "message": "Not authorized with Google",
                "provider_email": None,
                "expires_at": None
            }
        
        is_expired = is_token_expired(oauth_token)
        has_valid_token = get_valid_oauth_token(db, current_user.id, "google") is not None
        
        return {
            "authorized": True,
            "has_valid_token": has_valid_token,
            "is_expired": is_expired,
            "provider_email": oauth_token.provider_email,
            "provider_name": oauth_token.provider_name,
            "expires_at": oauth_token.expires_at,
            "scope": oauth_token.scope,
            "can_send_email": has_valid_token and "gmail.send" in (oauth_token.scope or "")
        }
        
    except Exception as e:
        logger.error(f"Error checking Google OAuth status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check OAuth status"
        )
