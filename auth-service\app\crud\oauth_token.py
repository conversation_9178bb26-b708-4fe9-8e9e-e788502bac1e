"""
CRUD operations for OAuth tokens
"""

from sqlalchemy.orm import Session
from datetime import datetime, timezone, timedelta
from typing import Optional, Dict, Any
import logging

from app.models.oauth_token import OAuthToken
from app.models.user import User

logger = logging.getLogger(__name__)


def create_or_update_oauth_token(
    db: Session,
    user_id: int,
    provider: str,
    token_data: Dict[str, Any]
) -> OAuthToken:
    """Create or update OAuth token for a user"""
    try:
        # Check if token already exists
        existing_token = db.query(OAuthToken).filter(
            OAuthToken.user_id == user_id,
            OAuthToken.provider == provider
        ).first()
        
        # Calculate expiry time
        expires_at = None
        if token_data.get('expires_in'):
            expires_at = datetime.now(timezone.utc) + timedelta(seconds=token_data['expires_in'])
        elif token_data.get('expires_at'):
            expires_at = datetime.fromtimestamp(token_data['expires_at'], tz=timezone.utc)
        
        if existing_token:
            # Update existing token
            existing_token.access_token = token_data['access_token']
            if token_data.get('refresh_token'):
                existing_token.refresh_token = token_data['refresh_token']
            existing_token.expires_at = expires_at
            existing_token.scope = token_data.get('scope')
            existing_token.is_active = True
            existing_token.updated_at = datetime.now(timezone.utc)
            
            # Update user info if provided
            user_info = token_data.get('user_info', {})
            if user_info:
                existing_token.provider_user_id = user_info.get('id')
                existing_token.provider_email = user_info.get('email')
                existing_token.provider_name = user_info.get('name')
                existing_token.provider_picture = user_info.get('picture')
            
            db.commit()
            db.refresh(existing_token)
            
            logger.info(f"Updated OAuth token for user {user_id}, provider {provider}")
            return existing_token
        else:
            # Create new token
            user_info = token_data.get('user_info', {})
            
            oauth_token = OAuthToken(
                user_id=user_id,
                provider=provider,
                access_token=token_data['access_token'],
                refresh_token=token_data.get('refresh_token'),
                expires_at=expires_at,
                scope=token_data.get('scope'),
                provider_user_id=user_info.get('id'),
                provider_email=user_info.get('email'),
                provider_name=user_info.get('name'),
                provider_picture=user_info.get('picture')
            )
            
            db.add(oauth_token)
            db.commit()
            db.refresh(oauth_token)
            
            logger.info(f"Created OAuth token for user {user_id}, provider {provider}")
            return oauth_token
            
    except Exception as e:
        logger.error(f"Error creating/updating OAuth token: {str(e)}")
        db.rollback()
        raise


def get_oauth_token(db: Session, user_id: int, provider: str) -> Optional[OAuthToken]:
    """Get OAuth token for a user and provider"""
    return db.query(OAuthToken).filter(
        OAuthToken.user_id == user_id,
        OAuthToken.provider == provider,
        OAuthToken.is_active == True
    ).first()


def get_valid_oauth_token(db: Session, user_id: int, provider: str) -> Optional[OAuthToken]:
    """Get valid (non-expired) OAuth token for a user and provider"""
    current_time = datetime.now(timezone.utc)
    
    return db.query(OAuthToken).filter(
        OAuthToken.user_id == user_id,
        OAuthToken.provider == provider,
        OAuthToken.is_active == True,
        OAuthToken.expires_at > current_time
    ).first()


def refresh_oauth_token(
    db: Session,
    user_id: int,
    provider: str,
    new_token_data: Dict[str, Any]
) -> Optional[OAuthToken]:
    """Refresh OAuth token with new token data"""
    try:
        oauth_token = get_oauth_token(db, user_id, provider)
        if not oauth_token:
            return None
        
        # Update token data
        oauth_token.access_token = new_token_data['access_token']
        
        # Calculate new expiry time
        if new_token_data.get('expires_in'):
            oauth_token.expires_at = datetime.now(timezone.utc) + timedelta(
                seconds=new_token_data['expires_in']
            )
        elif new_token_data.get('expires_at'):
            oauth_token.expires_at = datetime.fromtimestamp(
                new_token_data['expires_at'], tz=timezone.utc
            )
        
        oauth_token.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        db.refresh(oauth_token)
        
        logger.info(f"Refreshed OAuth token for user {user_id}, provider {provider}")
        return oauth_token
        
    except Exception as e:
        logger.error(f"Error refreshing OAuth token: {str(e)}")
        db.rollback()
        return None


def revoke_oauth_token(db: Session, user_id: int, provider: str) -> bool:
    """Revoke (deactivate) OAuth token for a user and provider"""
    try:
        oauth_token = get_oauth_token(db, user_id, provider)
        if not oauth_token:
            return False
        
        oauth_token.is_active = False
        oauth_token.updated_at = datetime.now(timezone.utc)
        
        db.commit()
        
        logger.info(f"Revoked OAuth token for user {user_id}, provider {provider}")
        return True
        
    except Exception as e:
        logger.error(f"Error revoking OAuth token: {str(e)}")
        db.rollback()
        return False


def delete_oauth_token(db: Session, user_id: int, provider: str) -> bool:
    """Delete OAuth token for a user and provider"""
    try:
        oauth_token = get_oauth_token(db, user_id, provider)
        if not oauth_token:
            return False
        
        db.delete(oauth_token)
        db.commit()
        
        logger.info(f"Deleted OAuth token for user {user_id}, provider {provider}")
        return True
        
    except Exception as e:
        logger.error(f"Error deleting OAuth token: {str(e)}")
        db.rollback()
        return False


def get_user_oauth_tokens(db: Session, user_id: int) -> list[OAuthToken]:
    """Get all OAuth tokens for a user"""
    return db.query(OAuthToken).filter(
        OAuthToken.user_id == user_id,
        OAuthToken.is_active == True
    ).all()


def is_token_expired(oauth_token: OAuthToken) -> bool:
    """Check if OAuth token is expired"""
    if not oauth_token.expires_at:
        return False  # No expiry set, assume valid
    
    return datetime.now(timezone.utc) >= oauth_token.expires_at
