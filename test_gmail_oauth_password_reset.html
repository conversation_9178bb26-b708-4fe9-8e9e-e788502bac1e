<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gmail OAuth Password Reset Test - Zionix</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .step {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .step.active {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .step.completed {
            border-left-color: #6c757d;
            background: #e2e3e5;
        }
        input, button, select {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s;
        }
        button:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        .google-btn {
            background: #db4437;
        }
        .google-btn:hover {
            background: #c23321;
        }
        .message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .oauth-status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .oauth-status.authorized {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .progress {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }
        .progress-step {
            flex: 1;
            text-align: center;
            padding: 10px;
            background: #e9ecef;
            margin: 0 5px;
            border-radius: 5px;
            font-weight: bold;
        }
        .progress-step.active {
            background: #28a745;
            color: white;
        }
        .progress-step.completed {
            background: #6c757d;
            color: white;
        }
        .token-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔐 ZIONIX</div>
            <h1>Gmail OAuth Password Reset Test</h1>
            <p>Test password reset flow using Google OAuth 2.0 and Gmail API</p>
        </div>

        <div class="progress">
            <div class="progress-step active" id="step1-progress">1. Login</div>
            <div class="progress-step" id="step2-progress">2. OAuth</div>
            <div class="progress-step" id="step3-progress">3. Send OTP</div>
            <div class="progress-step" id="step4-progress">4. Reset</div>
        </div>

        <!-- Step 1: Login -->
        <div class="step active" id="step1">
            <h3>Step 1: Login to Your Account</h3>
            <p>First, log in with your Zionix account to get authentication tokens.</p>
            
            <input type="email" id="loginEmail" placeholder="Your Email" required>
            <input type="password" id="loginPassword" placeholder="Your Password" required>
            
            <button onclick="loginUser()">Login</button>
            
            <div class="info">
                <strong>📝 Note:</strong> You need to be logged in to authorize Gmail access for sending OTP emails.
            </div>
        </div>

        <!-- Step 2: Google OAuth Authorization -->
        <div class="step" id="step2" style="display: none;">
            <h3>Step 2: Authorize Gmail Access</h3>
            <p>Authorize Zionix to send emails on your behalf using your Gmail account.</p>
            
            <div id="oauthStatus" class="oauth-status">
                <strong>OAuth Status:</strong> <span id="statusText">Checking...</span>
            </div>
            
            <button onclick="authorizeGmail()" class="google-btn">🔗 Authorize Gmail Access</button>
            <button onclick="checkOAuthStatus()">🔄 Refresh Status</button>
            
            <div class="info">
                <strong>🔐 Permissions Required:</strong>
                <ul>
                    <li>Send emails via Gmail API</li>
                    <li>Access your email address</li>
                    <li>Access your basic profile information</li>
                </ul>
            </div>
        </div>

        <!-- Step 3: Send OTP via Gmail -->
        <div class="step" id="step3" style="display: none;">
            <h3>Step 3: Send Password Reset OTP</h3>
            <p>Send an OTP to any email address using your authorized Gmail account.</p>
            
            <input type="email" id="targetEmail" placeholder="Email to send OTP to" required>
            
            <button onclick="sendOTPViaGmail()">📧 Send OTP via Gmail API</button>
            
            <div class="info">
                <strong>📧 How it works:</strong>
                <ul>
                    <li>OTP will be sent from your Gmail account</li>
                    <li>Professional email template with security warnings</li>
                    <li>6-digit code expires in 5 minutes</li>
                </ul>
            </div>
        </div>

        <!-- Step 4: Verify OTP and Reset Password -->
        <div class="step" id="step4" style="display: none;">
            <h3>Step 4: Verify OTP and Reset Password</h3>
            <p>Enter the OTP received via Gmail and set a new password.</p>
            
            <input type="text" id="otpCode" placeholder="Enter 6-digit OTP" maxlength="6" required>
            <input type="password" id="newPassword" placeholder="New Password" required>
            <input type="password" id="confirmNewPassword" placeholder="Confirm New Password" required>
            
            <button onclick="resetPassword()">🔑 Reset Password</button>
            
            <div class="info">
                <strong>✅ Final Step:</strong> After successful password reset, you can log in with your new password.
            </div>
        </div>

        <!-- Messages -->
        <div id="messages"></div>

        <!-- Token Information -->
        <div id="tokenInfo" class="token-info" style="display: none;">
            <h4>🔑 Token Information:</h4>
            <div id="tokenDetails"></div>
        </div>

        <!-- API Information -->
        <div class="info">
            <h4>🔧 API Endpoints Being Tested:</h4>
            <ul>
                <li><strong>POST /api/v1/login</strong> - User authentication</li>
                <li><strong>POST /api/v1/auth/google/authorize</strong> - Get OAuth authorization URL</li>
                <li><strong>POST /api/v1/auth/google/callback</strong> - Handle OAuth callback</li>
                <li><strong>GET /api/v1/auth/google/status</strong> - Check OAuth status</li>
                <li><strong>POST /api/v1/forgot-password-gmail</strong> - Send OTP via Gmail API</li>
                <li><strong>POST /api/v1/verify-otp</strong> - Verify OTP</li>
                <li><strong>POST /api/v1/reset-password</strong> - Reset password</li>
            </ul>
            <p><strong>Auth Service:</strong> <span id="serviceUrl">http://localhost:8002</span></p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8002/api/v1';
        let accessToken = '';
        let currentTargetEmail = '';

        function showMessage(message, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = message;
            messagesDiv.appendChild(messageDiv);
            
            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 10000);
        }

        function updateProgress(step) {
            // Reset all steps
            for (let i = 1; i <= 4; i++) {
                document.getElementById(`step${i}-progress`).className = 'progress-step';
                document.getElementById(`step${i}`).style.display = 'none';
                document.getElementById(`step${i}`).className = 'step';
            }
            
            // Update current step
            document.getElementById(`step${step}-progress`).className = 'progress-step active';
            document.getElementById(`step${step}`).style.display = 'block';
            document.getElementById(`step${step}`).className = 'step active';
            
            // Mark completed steps
            for (let i = 1; i < step; i++) {
                document.getElementById(`step${i}-progress`).className = 'progress-step completed';
                document.getElementById(`step${i}`).className = 'step completed';
            }
        }

        async function loginUser() {
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            if (!email || !password) {
                showMessage('❌ Please enter email and password.', 'error');
                return;
            }

            try {
                showMessage('🔄 Logging in...', 'loading');

                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams({
                        username: email,
                        password: password
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    accessToken = data.access_token;
                    showMessage('✅ Login successful!', 'success');
                    
                    // Show token info
                    document.getElementById('tokenInfo').style.display = 'block';
                    document.getElementById('tokenDetails').innerHTML = `
                        <strong>Access Token:</strong> ${accessToken.substring(0, 50)}...<br>
                        <strong>Token Type:</strong> ${data.token_type}<br>
                        <strong>Expires In:</strong> ${data.expires_in || 'N/A'} seconds
                    `;
                    
                    updateProgress(2);
                    checkOAuthStatus();
                } else {
                    showMessage(`❌ Login failed: ${data.detail || 'Invalid credentials'}`, 'error');
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function checkOAuthStatus() {
            if (!accessToken) {
                showMessage('❌ Please login first.', 'error');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/auth/google/status`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    const statusDiv = document.getElementById('oauthStatus');
                    const statusText = document.getElementById('statusText');
                    
                    if (data.authorized && data.can_send_email) {
                        statusDiv.className = 'oauth-status authorized';
                        statusText.innerHTML = `✅ Authorized (${data.provider_email})`;
                        showMessage('✅ Gmail access is authorized and ready!', 'success');
                        updateProgress(3);
                    } else if (data.authorized) {
                        statusDiv.className = 'oauth-status';
                        statusText.innerHTML = `⚠️ Authorized but missing Gmail send permission`;
                        showMessage('⚠️ Gmail authorization found but send permission missing. Please re-authorize.', 'error');
                    } else {
                        statusDiv.className = 'oauth-status';
                        statusText.innerHTML = `❌ Not authorized`;
                        showMessage('❌ Gmail access not authorized. Please authorize first.', 'error');
                    }
                } else {
                    showMessage(`❌ Failed to check OAuth status: ${data.detail}`, 'error');
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function authorizeGmail() {
            if (!accessToken) {
                showMessage('❌ Please login first.', 'error');
                return;
            }

            try {
                showMessage('🔄 Getting authorization URL...', 'loading');

                const response = await fetch(`${API_BASE}/auth/google/authorize`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        state: 'password_reset_test'
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage('🔗 Opening Google authorization page...', 'success');
                    
                    // Open authorization URL in new window
                    const authWindow = window.open(data.authorization_url, 'google_auth', 'width=500,height=600');
                    
                    // Poll for window closure (user completed auth)
                    const pollTimer = setInterval(() => {
                        if (authWindow.closed) {
                            clearInterval(pollTimer);
                            showMessage('🔄 Checking authorization status...', 'loading');
                            setTimeout(() => {
                                checkOAuthStatus();
                            }, 2000);
                        }
                    }, 1000);
                    
                } else {
                    showMessage(`❌ Failed to get authorization URL: ${data.detail}`, 'error');
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function sendOTPViaGmail() {
            const targetEmail = document.getElementById('targetEmail').value;

            if (!targetEmail) {
                showMessage('❌ Please enter target email address.', 'error');
                return;
            }

            if (!accessToken) {
                showMessage('❌ Please login first.', 'error');
                return;
            }

            currentTargetEmail = targetEmail;

            try {
                showMessage('📧 Sending OTP via Gmail API...', 'loading');

                const response = await fetch(`${API_BASE}/forgot-password-gmail`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${accessToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: targetEmail
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage('✅ OTP sent successfully via Gmail API! Check the target email inbox.', 'success');
                    updateProgress(4);
                } else if (response.status === 403) {
                    showMessage('❌ Gmail authorization required or insufficient permissions. Please authorize Gmail access first.', 'error');
                    updateProgress(2);
                } else {
                    showMessage(`❌ Failed to send OTP: ${data.detail}`, 'error');
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, 'error');
            }
        }

        async function resetPassword() {
            const otpCode = document.getElementById('otpCode').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmNewPassword = document.getElementById('confirmNewPassword').value;

            if (!otpCode || !newPassword || !confirmNewPassword) {
                showMessage('❌ Please fill in all fields.', 'error');
                return;
            }

            if (newPassword !== confirmNewPassword) {
                showMessage('❌ Passwords do not match.', 'error');
                return;
            }

            if (newPassword.length < 8) {
                showMessage('❌ Password must be at least 8 characters long.', 'error');
                return;
            }

            try {
                showMessage('🔄 Resetting password...', 'loading');

                const response = await fetch(`${API_BASE}/reset-password`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: currentTargetEmail,
                        otp_code: otpCode,
                        new_password: newPassword,
                        confirm_password: confirmNewPassword
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showMessage('🎉 Password reset successful! You can now log in with the new password.', 'success');
                } else {
                    showMessage(`❌ Password reset failed: ${data.detail}`, 'error');
                }
            } catch (error) {
                showMessage(`❌ Network error: ${error.message}`, 'error');
            }
        }

        // Check service availability on page load
        window.onload = async function() {
            try {
                const response = await fetch(`${API_BASE.replace('/api/v1', '')}/health`);
                if (response.ok) {
                    showMessage('✅ Auth service is running and ready for testing!', 'success');
                } else {
                    showMessage('⚠️ Auth service may not be running. Please start it with: docker-compose -f docker-compose.hybrid.yml up -d', 'error');
                }
            } catch (error) {
                showMessage('❌ Cannot connect to auth service. Please ensure it is running on localhost:8002', 'error');
            }
        };
    </script>
</body>
</html>
